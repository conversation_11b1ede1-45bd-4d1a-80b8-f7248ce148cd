import React from "react";
import PropTypes from "prop-types";
import { FormattedMessage, useIntl } from "react-intl";
import { useQuery } from "@apollo/client";
import Select from "react-select";
import { CircularProgress } from "@material-ui/core";
import { Features } from "gql/queries/getParent.gql";
import { ExtraServices } from "gql/queries/ExtraService.gql";

import { persist } from "constants/constants";
import { set } from "react-hook-form";

export default function ExtraServiceDropDown({
  loading,
  setSelectedExtraService,
  selectedExtraService,
  error,
  multiple,
  valueAttribute,
  ...props
}) {
 
  const { data: allservices, loading: gettingServices } = useQuery(ExtraServices, {
    variables: { limit:1000, isActive: true },
  });
  
  const { locale, formatMessage } = useIntl();
  const options =
  allservices?.extraServices?.collection?.map((x) => ({
      value: x[valueAttribute || "id"],
      label: x[`${locale}Title`],
    })) || [];
  if (options.length) {
    options.unshift({ value: "all", label: <FormattedMessage id="ALL" /> });
  }
  React.useEffect(() => {
    if (!selectedExtraService) {
      onClear();
    }
  }, [selectedExtraService]);

  const selectInputRef = React.useRef();

  const onClear = () => {
    selectInputRef.current.select.clearValue();
  };
  console.log(allservices?.extraServices?.collection,"allservices")
  return (
    <Select
    
      className={`dropdown-select ${error ? "selection-error" : ""}`}
      options={options}
      isMulti={multiple}
      isClearable
      ref={selectInputRef}
      loadOptions={gettingServices || loading}
      defaultValue={options.find((optn) => `${optn.value}` === `${selectedExtraService}`)}
      value= { multiple ? 
        options?.filter((optn) => selectedExtraService?.includes(+optn.value))
        : options.find((optn) => `${optn.value}` === `${selectedExtraService}`)}
      placeholder={formatMessage({ id: "extraservice" })}
    
      onChange={(selection) => {
        if (multiple) {
          const extraids = [];
          if (selection == null && multiple) {
            setSelectedExtraService();
            return;
          }
          if (selection[0].value == "all" || selection[selection.length - 1].value == "all") {
            options.map(
              (onselectoion) => onselectoion.value != "all" && extraids.push(+onselectoion?.value),
            );
          }
          selection?.map((onselectoion) => extraids.push(+onselectoion?.value));
          if (extraids.length) {
            const extras = extraids.filter((id) => !isNaN(id));
            setSelectedExtraService([...extras]);
          } else {
            setSelectedExtraService([]);
          }
        } else {
          if (selection?.value == "all") {
            setSelectedExtraService("null");
            return;
          }
          setSelectedExtraService(+selection?.value);
        }
      }}
      noOptionsMessage={() => {
        if (gettingServices) {
          return <CircularProgress />;
        }
        if (!options?.length) return "no data found";
      }}
      {...props}
    />
  );
}
ExtraServiceDropDown.propTypes = {
  valueAttribute: PropTypes.string,
  loading: PropTypes.bool,
  selectedExtraService: PropTypes.string,
  setSelectedExtraService: PropTypes.func,
  error: PropTypes.oneOfType([PropTypes.bool, PropTypes.string]),
};
