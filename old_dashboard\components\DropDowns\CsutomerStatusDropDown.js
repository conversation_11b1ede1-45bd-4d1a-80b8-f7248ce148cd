/* eslint-disable prettier/prettier */
import React from "react";
import PropTypes from "prop-types";
import { useIntl } from "react-intl";
import { useQuery } from "@apollo/client";
import Select from "react-select";
import { CircularProgress } from "@material-ui/core";
import { persist } from "constants/constants";
import { GetMakesQueryForSelect } from "gql/queries/Cars.queries.gql";
import { CustomerStatuses } from "gql/queries/CustomerStatus.gql";

export function CustomerStatusDDL({
  loading,
  setSelectedCustomerStatus,
  selectedCustomerStatus,
  error,
  valueAttribute,
  inBooking,
  ...props
}) {
  const { data: makesRes, loading: gettingMakes } = useQuery(GetMakesQueryForSelect, {
    variables: { limit: persist.unlimitedLimit },
  });
  const { data: customerstatus } = useQuery(CustomerStatuses);

  const { locale, formatMessage } = useIntl();

  const options =
  customerstatus?.customerStatuses?.map((x) => ({
      value: x.key,
      label: x.value,
    })) || [];

  React.useEffect(() => {
    if (!selectedCustomerStatus) {
      onClear();
    }
  }, [selectedCustomerStatus]);

  const selectInputRef = React.useRef();

  const onClear = () => {
    selectInputRef.current.select.clearValue();
  };
  if(inBooking){
    options.unshift({value:"all",label:formatMessage({ id: "all" })})

  }

  return (
    <Select
      className={`dropdown-select ${error ? "selection-error" : ""}`}
      options={options}
      ref={selectInputRef}
      isMulti={inBooking}
      isClearable
      loadOptions={gettingMakes || loading}
      defaultValue={options.find((optn) => `${optn.value}` === `${selectedCustomerStatus}`)}
      value={options.find((optn) => `${optn.value}` === `${selectedCustomerStatus}`)}
      placeholder={formatMessage({ id: "Customertype" })}
      onChange={(selection) => {
        if(inBooking &&  selection?.[selection?.length - 1]?.value == "all"){
          const makes = selection?.filter((onselectoion) => onselectoion.value == "all");
  
          setSelectedCustomerStatus(makes)
            return
          }else{
            if(inBooking){
              const makesWithoutAll= selection?.filter((onselectoion) => onselectoion.value != "all");
  
              setSelectedCustomerStatus(makesWithoutAll)
            }else{
              setSelectedCustomerStatus(selection?.value)
            }
         
            return
          }
        // if(inBooking){
        //   setSelectedCustomerStatus(selection)
        // }else{
        // setSelectedCustomerStatus(selection?.value);

        // }
      }}
      noOptionsMessage={() => {
        if (gettingMakes) {
          return <CircularProgress />;
        }
        if (!options?.length) return "no data found";
      }}
      {...props}
    />
  );
}
CustomerStatusDDL.propTypes = {
  valueAttribute: PropTypes.string,
  loading: PropTypes.bool,
  selectedCustomerStatus: PropTypes.string,
  setSelectedCustomerStatus: PropTypes.func,
  error: PropTypes.oneOfType([PropTypes.bool, PropTypes.string]),
};
