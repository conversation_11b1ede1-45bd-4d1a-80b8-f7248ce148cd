/**
 * Tiny Pie Chart
 */
import React from "react";
import { Pie } from "react-chartjs-2";
const TinyPieChart = ({ labels, datasets, width, height ,dataOnject}) => {
    const options = {
        legend: {
          display: false,
        },
        tooltips: {
          enabled: true,
          mode: "nearest",
          callbacks: {
            label: function (tooltipItem, _data) {
              var value = dataOnject[tooltipItem.index];
              var label = labels[tooltipItem.index];
              console.log(label,"test")
              return `${label}: ${value}`;
            },
          },
        },
      };
  console.log(datasets, "datasets ....");
  const data = {
    labels,
    datasets,
  };
  return <Pie  height={height} width={width} data={data} options={options} />;
};

export default TinyPieChart;
