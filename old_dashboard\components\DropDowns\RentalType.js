/* eslint-disable prettier/prettier */
/* eslint-disable react/prop-types */
import React from "react";
import { AllAreas } from "gql/queries/Areas.queries.gql";
import PropTypes from "prop-types";
import { useIntl } from "react-intl";
import { useQuery } from "@apollo/client";
import Select from "react-select";
import { CircularProgress } from "@material-ui/core";
import { RentTypeList,RentTypeListForBooking } from "constants/constants";

export function RentTypeDropDown({
  loading,
  setSelectRentType,
  selectRentType,
  valueAttribute,
  multiple,
  error,
  required,
  setList,
  inBooking,
  forBooking,
  ...props
}) {
  const { locale, formatMessage } = useIntl();
 
  const options = forBooking ? RentTypeListForBooking(formatMessage) :  RentTypeList(formatMessage) 


  React.useEffect(() => {
    if (!selectRentType) {
      onClear();
    }
  }, [selectRentType]);


  const selectInputRef = React.useRef();

  const onClear = () => {
    selectInputRef.current.select.clearValue();
  };
  return (
    options && (
      <Select
        className={`dropdown-select ${multiple ? "multiple" : ""}  ${required ? "required" : ""} ${
          error ? "selection-error" : ""
        }`}
        options={[...options]}
        ref={selectInputRef}
        isClearable
        isMulti={multiple || inBooking}
        defaultValue={options.find((optn) => `${optn.value}` === `${selectRentType}`)}
        value={
        
            options.find((optn) => `${optn.value}` === `${selectRentType}`)
        }
        placeholder={formatMessage({ id: "components.rent0wn" })}
        onChange={(selection) => {
       setSelectRentType(selection?.value)
        }}
        noOptionsMessage={() => {
         
          if (!options?.length) return "no data found";
        }}
        {...props}
      />
    )
  );
}

RentTypeDropDown.propTypes = {
  valueAttribute: PropTypes.string.isRequired,
  loading: PropTypes.bool,
  error: PropTypes.bool,
  selectRentType: PropTypes.any,
  setSelectRentType: PropTypes.func,
  multiple: PropTypes.bool,
};
