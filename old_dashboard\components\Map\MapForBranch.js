/* eslint-disable prettier/prettier */
/* eslint-disable no-console */
/* eslint-disable consistent-return */
import React, { useState, useEffect } from "react";
import { useIntl } from "react-intl";
import Geocode from "react-geocode";
import { Map, InfoWindow, GoogleApiWrap<PERSON>, <PERSON><PERSON>,Polygon, Circle } from "google-maps-react";
import PropTypes from "prop-types";
import "./style.css";
const options = {
  enableHighAccuracy: true,
  timeout: 5000,
  maximumAge: 0,
};

function MapContainer(props) {
  const { messages, locale } = useIntl();
  Geocode.setApiKey(process.env.REACT_APP_MAP_API);
  Geocode.setLanguage(locale);

  const [showingInfoWindow, setShowingInfoWindow] = useState(true);
  const [activeMarker, setActiveMarker] = useState();
  const [selectedPlace, setSelectedPlace] = useState({});
  const [address, setAddress] = useState("");
  const [userSelectionPosition, setUserSelectionPosition] = useState();
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [editLocationSet, seteditLocationSet] = useState(false);
  const initLat = props.latitude || 24.73265591475684;
  const initLng = props.longitude || 46.69235839843748;

  const onMapClicked = (map, maps, e) => {
    setShowingInfoWindow(true);
    setSelectedLocation(false);
    setSelectedLocation(null);
    const { latLng } = e;
    const latitude = latLng.lat();
    const longitude = latLng.lng();
    getGeocode(latitude, longitude);
    setActiveMarker(null);
    setSelectedPlace({});
  };

  useEffect(() => {
    if (props.getSearchPlace) {
      const lat = props?.getSearchPlace?.geometry?.location?.lat();
      const lng = props?.getSearchPlace?.geometry?.location?.lng();
      setSelectedPlace({});
      setShowingInfoWindow(false);
      getGeocode(lat, lng);
      setShowingInfoWindow(true);
    }
  }, [props.getSearchPlace]);

  useEffect(() => {
    if (!props.longitude && !props.latitude) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          getGeocode(position.coords.latitude, position.coords.longitude);
          setActiveMarker(null);
          setSelectedPlace({});
        },
        (error) => {
          if (error.code === 1) {
            getGeocode(initLat, initLng);
            setActiveMarker(null);
            setSelectedPlace({});
          }
          if (error.code === 2) {
            // props.setErrorMessage(true);
          }
        },
        options,
      );
    }
  }, []);
  useEffect(() => {
    if ((props.centerlat, props.centerlng)) {
      getGeocode(props.centerlat, props.centerlng);
      seteditLocationSet(true);
    }
  }, [props.centerlat, props.centerlng]);

  useEffect(() => {
    if (props.latitude && props.longitude && !editLocationSet) {
      getGeocode(props.latitude, props.longitude);
      seteditLocationSet(true);
    }
  }, [props.latitude, props.longitude]);

  function getGeocode(newLat, newLng) {
    Geocode.fromLatLng(newLat, newLng)
      .then((response) => {
        if (locale == "ar" && props?.setDistrictNameAr) {
          props?.setDistrictNameAr?.(response.results[0].address_components?.[2]?.long_name);
          props.setDistrictNameEn?.(response.results[0].address_components?.[2]?.long_name);
          Geocode.setLanguage("en");
 Geocode.fromLatLng(newLat, newLng).then((response) => {
   props?.setDistrictNameEn?.(response.results[0].address_components?.[2]?.long_name);
   props.setLatitude(newLat);
   props.setLongitude(newLng);
});
        } else if (props?.setDistrictNameEn) {
          props.setDistrictNameEn?.(response.results[0].address_components?.[2]?.long_name);
           Geocode.setLanguage("ar");
  Geocode.fromLatLng(newLat, newLng).then((response) => {
    props?.setDistrictNameAr?.(response.results[0].address_components?.[2]?.long_name);
});
props.setLatitude(newLat);
props.setLongitude(newLng);
        }
        setAddress(response.results[0].address_components?.[2]?.long_name);
        setUserSelectionPosition({ lat: newLat, lng: newLng });
        props.setDeliverAddress(response.results[0].address_components?.[2]?.long_name);

        props.setLatitude(newLat);
        props.setLongitude(newLng);
      })
      .then(() => {
        if (locale == "en") {
          Geocode.setLanguage("ar");
          Geocode.fromLatLng(newLat, newLng).then((response) => {
              props?.setDistrictNameAr?.(response.results[0].address_components?.[2]?.long_name);
          });
        } else {
          

          Geocode.setLanguage("en");
          Geocode.fromLatLng(newLat, newLng).then((response) => {
             props?.setDistrictNameEn?.(response.results[0].address_components?.[2]?.long_name);
          });
        }
        Geocode.setLanguage(locale);
      })
      .catch((err) => console.error("err", err));
  }
  const { google, mapPosition } = props;
  const style = {
    width: "100%",
    height: "100%",
  };

  // const infoWindow = selectedLocation ? (
  //   <InfoWindow
  //     position={{
  //       lat: selectedLocation.location.lat + 0.3,
  //       lng: selectedLocation.location.long,
  //     }}
  //     marker={true}
  //     visible={selectedLocation}
  //   >
  //     <div className="infoWindow">
  //       <h3 className="info-title">{selectedLocation.name}</h3>
  //       <p className="info-address">
  //         <i className="material-icons">{messages["map.location"]}</i>
  //         {selectedLocation.location.address}
  //       </p>
  //     </div>
  //   </InfoWindow>
  // ) : (
  //   <InfoWindow
  //     position={{
  //       lat: userSelectionPosition && userSelectionPosition,
  //       lng: userSelectionPosition && userSelectionPosition,
  //     }}
  //     marker={activeMarker}
  //     visible={showingInfoWindow}
  //   >
  //     <div className="infoWindow" >
  //       <p className="info-address">
  //         <i className="material-icons">{messages["map.location"]}</i>
  //         {selectedPlace.address || address}
  //       </p>
  //     </div>
  //   </InfoWindow>
  // );
  const triangleCoords =[ 
    {lat: 24.2670, lng: 46.4927},
    {lat: 24.2680, lng: 46.6456},
    {lat: 24.1109, lng: 46.6777},
    {lat: 24.0377, lng: 46.6491},
    {lat: 24.0008, lng: 46.6709},
    {lat: 23.9672, lng: 46.6473},
    {lat: 23.9133, lng: 46.6408},
    {lat: 23.8915, lng: 46.6155},
    {lat: 23.8451, lng: 46.6144},
    {lat: 23.7969, lng: 46.6557},
    {lat: 23.7800, lng: 46.6466},
    {lat: 23.7526, lng: 46.6619},
    {lat: 23.7293, lng: 46.6523},
    {lat: 23.6670, lng: 46.6782},
    {lat: 23.6449, lng: 46.6697},
    {lat: 23.6207, lng: 46.6863},
    {lat: 23.5808, lng: 46.6579},
    {lat: 23.6045, lng: 46.6365},
    {lat: 23.5824, lng: 46.5994},
    {lat: 23.5999, lng: 46.5853},
    {lat: 23.5808, lng: 46.5582},
    {lat: 23.5607, lng: 46.5632},
    {lat: 23.5339, lng: 46.5368},
    {lat: 23.5373, lng: 46.5277},
    {lat: 23.5085, lng: 46.4845},
    {lat: 23.4948, lng: 46.4892},
    {lat: 23.4875, lng: 46.4649},
    {lat: 23.5075, lng: 46.4435},
    {lat: 23.5348, lng: 46.4467},
    {lat: 23.5569, lng: 46.4250},
    {lat: 23.5753, lng: 46.4408},
    {lat: 23.5981, lng: 46.4449},
    {lat: 23.6433, lng: 46.4700},
    {lat: 23.6698, lng: 46.4726},
    {lat: 23.7182, lng: 46.4554},
    {lat: 23.8126, lng: 46.4544},
    {lat: 23.8566, lng: 46.4372},
    {lat: 23.8737, lng: 46.4564},
    {lat: 23.9074, lng: 46.4480},
    {lat: 23.9458, lng: 46.4487},
    {lat: 23.9796, lng: 46.4358},
    {lat: 24.0160, lng: 46.4698},
    {lat: 24.0432, lng: 46.4653},
    {lat: 24.0678, lng: 46.4752},
    {lat: 24.0979, lng: 46.4737},
    {lat: 24.1376, lng: 46.4921},
    {lat: 24.1641, lng: 46.4699},
    {lat: 24.2169, lng: 46.4676},
    {lat: 24.2412, lng: 46.4929},
    {lat: 24.2670, lng: 46.4927}
  ]

  
  return (
    <Map
      google={google}
      initialCenter={{
        lat: userSelectionPosition || initLat,
        lng: userSelectionPosition || initLng,
      }}
      center={userSelectionPosition}
      style={style}
      zoom={7}
      onClick={onMapClicked}
    >
        {/* <Polygon
          paths={triangleCoords}
          strokeColor="#0000FF"
          strokeOpacity={0.8}
          strokeWeight={2}
          fillColor="#0000FF"
          fillOpacity={0.35} 
          onClick={()=>alert("karem")}
          /> */}
      
      <Marker
        onClick={onMapClicked}
        position={{
          lat: userSelectionPosition || initLat,
          lng: userSelectionPosition || initLng,
        }}
        style={{ zUndex: 1 }}
        name={address}
      />
      {/* {infoWindow} */}
    </Map>
  );
}

const LoadingContainer = () => <main className="loader" />;

MapContainer.propTypes = {
  google: PropTypes.object,
  mapPosition: PropTypes.object,
  getSearchPlace: PropTypes.object,
  setLatitude: PropTypes.func,
  setDeliverAddress: PropTypes.func,
  setDistrictNameAr: PropTypes.func,
  setDistrictNameEn: PropTypes.func,
  setLongitude: PropTypes.func,
  latitude: PropTypes.string,
  longitude: PropTypes.string,
};

export default GoogleApiWrapper((props) => ({
  apiKey: process.env.REACT_APP_MAP_API,
  language: props.locale,
  LoadingContainer,
}))(MapContainer);
