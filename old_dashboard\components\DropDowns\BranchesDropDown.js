/* eslint-disable no-restricted-globals */
/* eslint-disable no-unused-expressions */
/* eslint-disable no-nested-ternary */
/* eslint-disable prettier/prettier */
/* eslint-disable react/prop-types */
import React, { memo, useEffect, useState } from "react";
import PropTypes from "prop-types";
import { FormattedMessage, useIntl } from "react-intl";
import { useQuery } from "@apollo/client";
import Select from "react-select";
import { CircularProgress } from "@material-ui/core";
import { Branches } from "gql/queries/AllBranches.gql";
import { persist } from "constants/constants";

function BranchesDropDown({
  loading,
  setSelectedBranch,
  selectedBranch,
  error,
  valueAttribute,
  allyId,
  multiple,
  required,
  isAlly,
  manageradd,
  areaIds,
  noSkip,
  isDisabled,
  coupon,
  BranchesDropDown,
  setList,
  banner,
  cities,
  ...props
}) {
  const { data: allbranches, loading: gettingModels } = useQuery(Branches, {
    skip: !allyId?.length && coupon,
    variables: {
      limit: persist.higherUnlimited,
      allyCompanyId: allyId || isAlly,
      allyCompanyIds: allyId || isAlly,
      isActive: coupon ? true : undefined,
      areaIds: cities,
    },
  });
  useEffect(() => {
    if (!selectedBranch) {
      onClear();
    }
  }, [selectedBranch]);

  const selectInputRef = React.useRef();

  const onClear = () => {
    selectInputRef.current.select.clearValue();
  };
  const { formatMessage } = useIntl();
  const [options, setOptions] = useState([]);

  useEffect(() => {
    if (allbranches && setList) {
      setList(allbranches?.branches?.collection?.length);
    }
  }, [allbranches]);

  useEffect(() => {
    const alloptions =
      allbranches?.branches.collection?.map((x) => ({
        value: x[valueAttribute || "enName"],
        label: x.name,
      })) || [];
    if (alloptions.length && multiple) {
      setOptions([{ value: "all", label: formatMessage({ id: "widgets.all" }) }, ...alloptions]);
    }else if(alloptions.length){
      setOptions([...alloptions]);

    }
    else {
      setOptions([]);
    }
  }, [allbranches]);

  return (
    <Select
      ref={selectInputRef}
      isMulti={multiple}
      isDisabled={isDisabled}
      isClearable
      className={`dropdown-select ${multiple ? "multiple" : ""}  ${required ? "required" : ""} ${
        error ? "selection-error" : ""
      }`}
      options={options}
      loadOptions={gettingModels || loading}
      value={
        multiple
          ? options?.filter((optn) => selectedBranch?.includes(+optn.value))
          : options?.find((optn) => `${optn.value}` === `${selectedBranch}`)
      }
      placeholder={
        coupon || banner
          ? formatMessage({ id: "ally.branches" })
          : formatMessage({ id: "branches" })
      }
      onChange={(selection) => {
        if (multiple) {
          const branchids = [];
          if (selection == null && multiple) {
            setSelectedBranch();
            return;
          }
          if (selection[0].value == "all" || selection[selection.length - 1].value == "all") {
            options.map(
              (onselectoion) => onselectoion.value != "all" && branchids.push(+onselectoion?.value),
            );
          }
          selection?.map((onselectoion) => branchids.push(+onselectoion?.value));
          if (branchids.length) {
            const versions = branchids.filter((id) => !isNaN(id));
            setSelectedBranch([...versions]);
          } else {
            setSelectedBranch([]);
          }
        } else {
          if (selection?.value == "all") {
            setSelectedBranch("null");
            return;
          }
          setSelectedBranch(+selection?.value);
        }
      }}
      noOptionsMessage={() => {
        if (gettingModels) {
          return <CircularProgress />;
        }
        if (!allbranches?.length) return <FormattedMessage id="No data found" />;
      }}
      {...props}
    />
  );
}
BranchesDropDown.propTypes = {
  valueAttribute: PropTypes.string,
  loading: PropTypes.bool,
  selectedBranch: PropTypes.string,
  setSelectedBranch: PropTypes.func,
  error: PropTypes.oneOfType([PropTypes.bool, PropTypes.string]),
};
export default memo(BranchesDropDown);
