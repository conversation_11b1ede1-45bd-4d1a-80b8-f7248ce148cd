import { gql } from '@apollo/client';

// Create Business Rental Mutation
export const CREATE_BUSINESS_RENTAL = gql`
  mutation CreateBusinessRental(
    $additionalNotes: String
    $carModelId: ID
    $carVersionId: ID
    $insuranceId: ID!
    $makeId: ID
    $numberOfCars: Int!
    $numberOfMonths: Int!
    $otherCarName: String
    $pickUpCityId: ID!
    $pickUpDatetime: String!
    $userId: ID!
  ) {
    createBusinessRental(
      additionalNotes: $additionalNotes
      carModelId: $carModelId
      carVersionId: $carVersionId
      insuranceId: $insuranceId
      makeId: $makeId
      numberOfCars: $numberOfCars
      numberOfMonths: $numberOfMonths
      otherCarName: $otherCarName
      pickUpCityId: $pickUpCityId
      pickUpDatetime: $pickUpDatetime
      userId: $userId
    ) {
      businessRental {
        additionalNotes
        arBusinessActivity
        arMakeName
        arModelName
        arPickUpCityName
        businessActivityId
        businessActivityName
        carImage
        carModelId
        carVersionId
        commercialRegistrationCertificate
        commercialRegistrationNo
        companyEmail
        companyName
        companyPhone
        enBusinessActivity
        enMakeName
        enModelName
        enPickUpCityName
        id
        insuranceId
        makeId
        makeName
        modelName
        numberOfCars
        numberOfMonths
        otherCarName
        phone
        pickUpCityId
        pickUpCityName
        pickUpDatetime
        userId
        year
      }
    }
  }
`;

// Update Business Rental Mutation
export const UPDATE_BUSINESS_RENTAL = gql`
  mutation UpdateBusinessRental(
    $additionalNotes: String
    $carModelId: ID
    $carVersionId: ID
    $insuranceId: ID!
    $businessRentalId: ID!
    $makeId: ID
    $numberOfCars: Int!
    $numberOfMonths: Int!
    $otherCarName: String
    $pickUpCityId: ID!
    $pickUpDatetime: String!
  ) {
    updateBusinessRental(
      additionalNotes: $additionalNotes
      carModelId: $carModelId
      carVersionId: $carVersionId
      insuranceId: $insuranceId
      businessRentalId: $businessRentalId
      makeId: $makeId
      numberOfCars: $numberOfCars
      numberOfMonths: $numberOfMonths
      otherCarName: $otherCarName
      pickUpCityId: $pickUpCityId
      pickUpDatetime: $pickUpDatetime
    ) {
      businessRental {
        additionalNotes
        arBusinessActivity
        arMakeName
        arModelName
        arPickUpCityName
        businessActivityId
        businessActivityName
        carImage
        carModelId
        carVersionId
        commercialRegistrationCertificate
        commercialRegistrationNo
        companyEmail
        companyName
        companyPhone
        enBusinessActivity
        enMakeName
        enModelName
        enPickUpCityName
        id
        insuranceId
        makeId
        makeName
        modelName
        numberOfCars
        numberOfMonths
        otherCarName
        phone
        pickUpCityId
        pickUpCityName
        pickUpDatetime
        userId
        year
      }
    }
  }
`;

// Create Business Rental Offer Mutation
export const CREATE_BUSINESS_RENTAL_OFFER = gql`
  mutation CreateBusinessRentalOffer(
    $businessRentalId: ID
    $additionalKilometer: Float!
    $allyCompanyId: ID!
    $carInsuranceFull: Float
    $carInsuranceStandard: Float
    $kilometerPerMonth: Float!
    $offerPrice: Float!
  ) {
    createBusinessRentalOffer(
      businessRentalId: $businessRentalId
      additionalKilometer: $additionalKilometer
      allyCompanyId: $allyCompanyId
      carInsuranceFull: $carInsuranceFull
      carInsuranceStandard: $carInsuranceStandard
      kilometerPerMonth: $kilometerPerMonth
      offerPrice: $offerPrice
    ) {
      businessRentalOffer {
        status
      }
      errors
      status
    }
  }
`;

// Assign Business Rental To User Mutation
export const ASSIGN_BUSINESS_RENTAL_TO = gql`
  mutation AssignBusinessRentalTo($businessRentalId: ID!, $userId: ID!) {
    assignBusinessRentalTo(businessRentalId: $businessRentalId, userId: $userId) {
      errors
      status
    }
  }
`;

// Assign Business Rental To Me Mutation
export const ASSIGN_BUSINESS_RENTAL_TO_ME = gql`
  mutation AssignBusinessRentalToMe($businessRentalId: ID!) {
    assignBusinessRentalToMe(businessRentalId: $businessRentalId) {
      errors
      status
    }
  }
`;
