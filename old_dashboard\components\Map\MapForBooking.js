/* eslint-disable prettier/prettier */
/* eslint-disable eqeqeq */
/* eslint-disable no-unused-expressions */
/* eslint-disable react/prop-types */
/* eslint-disable no-console */
/* eslint-disable consistent-return */
import React, { useState, useEffect } from "react";
import { useIntl } from "react-intl";
import Geocode from "react-geocode";
import { Map, GoogleApiWrapper, Marker } from "google-maps-react";
import PropTypes from "prop-types";
import "./style.css";
const options = {
  enableHighAccuracy: true,
  timeout: 5000,
  maximumAge: 0,
};

function MapContainer(props) {
  const { locale } = useIntl();
  Geocode.setApiKey(process.env.REACT_APP_MAP_API);
  Geocode.setLanguage(locale);

  const [address, setAddress] = useState("");
  const [userSelectionPosition, setUserSelectionPosition] = useState();
  const initLat = props.latitude || 24.73265591475684;
  const initLng = props.longitude || 46.69235839843748;

  const onMapClicked = (map, maps, e) => {
    const { latLng } = e;
    const latitude = latLng?.lat();
    const longitude = latLng?.lng();
    getGeocode(latitude, longitude);
  };

  useEffect(() => {
    if (props.getSearchPlace) {
      const lat = props?.getSearchPlace?.geometry?.location?.lat();
      const lng = props?.getSearchPlace?.geometry?.location?.lng();
      getGeocode(lat, lng);
    }
  }, [props.getSearchPlace]);

  useEffect(() => {
    if (!props.longitude && !props.latitude) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          getGeocode(position.coords.latitude, position.coords.longitude);
        },
        (error) => {
          if (error.code === 1) {
            getGeocode(initLat, initLng);
          }
        },
        options,
      );
    }
  }, []);
  useEffect(() => {
    if (props.mapChange) {
      getGeocode(props.latitude, props.longitude);
    }
  }, [props.mapChange]);

  function getGeocode(newLat, newLng) {
    Geocode.fromLatLng(newLat, newLng)
      .then((response) => {
        if (locale == "ar" && props?.setDistrictNameAr) {
          props?.setDistrictNameAr?.(response.results[0].address_components?.[2]?.long_name);
        } else if (props?.setDistrictNameEn) {
          props.setDistrictNameEn?.(response.results[0].address_components?.[2]?.long_name);
        }
        setAddress(response.results[0].address_components?.[2]?.long_name);
        setUserSelectionPosition({ lat: newLat, lng: newLng });
        props.setDeliverAddress(response.results[0].address_components?.[2]?.long_name);

        props.setLatitude(newLat);
        props.setLongitude(newLng);
      })
      .then(() => {
        if (locale == "en") {
          Geocode.setLanguage("ar");
          Geocode.fromLatLng(newLat, newLng).then((response) => {
            props?.setDistrictNameAr?.(response.results[0].address_components?.[2]?.long_name);
          });
        } else {
          Geocode.setLanguage("en");
          Geocode.fromLatLng(newLat, newLng).then((response) => {
            props?.setDistrictNameEn?.(response.results[0].address_components?.[2]?.long_name);
          });
        }
        Geocode.setLanguage(locale);
        props.setMapChange(false);
      })
      .catch((err) => console.error("err", err));
  }
  const { google } = props;
  const style = {
    width: "100%",
    height: "100%",
  };

  return (
    <Map
      google={google}
      initialCenter={{
        lat: userSelectionPosition || initLat,
        lng: userSelectionPosition || initLng,
      }}
      center={userSelectionPosition}
      style={style}
      zoom={12}
      onClick={onMapClicked}
    >
      <Marker
        onClick={onMapClicked}
        position={{
          lat: userSelectionPosition || initLat,
          lng: userSelectionPosition || initLng,
        }}
        style={{ zUndex: 1 }}
        name={address}
      />
      {/* {infoWindow} */}
    </Map>
  );
}

const LoadingContainer = () => <main className="loader" />;

MapContainer.propTypes = {
  google: PropTypes.object,
  getSearchPlace: PropTypes.object,
  setLatitude: PropTypes.func,
  setDeliverAddress: PropTypes.func,
  setDistrictNameAr: PropTypes.func,
  setDistrictNameEn: PropTypes.func,
  setLongitude: PropTypes.func,
  latitude: PropTypes.string,
  longitude: PropTypes.string,
};

export default GoogleApiWrapper((props) => ({
  apiKey: process.env.REACT_APP_MAP_API,
  language: props.locale,
  LoadingContainer,
}))(MapContainer);
