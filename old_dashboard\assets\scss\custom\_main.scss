/*======== App Global (Main Structure) Mention Here =======*/
#root,
.app {
  height: 100%;
  width: 100%;
}
.app-main-container {
  display: flex;
  flex-wrap: nowrap;
}
.rct-page-content {
  padding: $grid-gutter-width; //24px;
}
.rct-footer {
  box-shadow: $footer-box-shadow;
  background-color: $footer-bg;
  padding: 1.25rem; //20px;
  z-index: 9;
  h5 {
    margin-bottom: 0;
    color: $gray-600;
    font-size: 0.875rem; //14px;
  }
}
.rct-block {
  background-color: $block-bg;
  border-radius: $block-border-radius;
  box-shadow: $block-shadow;
  border: $block-border;
  transition: $block-transition;
  margin-bottom: $block-margin-bottom;
  position: relative;
  .rct-block-content {
    padding: $block-spacing;
  }
  &:hover {
    box-shadow: $block-hover-shadow;
  }
}
.rct-block-title {
  padding: $block-title-spacing;
  position: relative;
  // @include border(1px solid, $gray-300, bottom);
  .badge {
    vertical-align: middle;
  }
  h4 {
    margin-bottom: 0;
    color: $block-title-color;
    font-size: $block-title-font-size;
    text-transform: $block-title-transform;
    font-weight: $block-title-font-weight;
    line-height: 30px;
  }
  .contextual-link {
    position: absolute;
    top: 20px;
    right: 20px;
    a {
      color: $block-contextual-color;
      font-size: $block-contextual-font-size;
      margin-right: 15px;
      &:hover {
        color: $blue;
      }
      &:last-child {
        margin-right: 0;
      }
    }
  }
}
.rct-block-footer {
  background-color: $block-bg;
  padding: $block-title-spacing;
  border-top: $block-footer-border-width solid $block-footer-border-color;
}

.card-footer {
  background-color: $block-bg;
}
.sub-title {
  padding: $block-title-spacing;
  margin-bottom: 1.25rem; //20px;
  h4 {
    margin-bottom: 0;
    color: $block-title-color;
    font-size: $block-title-font-size;
    text-transform: $block-title-transform;
    font-weight: $block-title-font-weight;
  }
}
[class*="gradient-"],
[class^="gradient-"] {
  h4 {
    color: $white;
    border-color: $white;
  }
}
/*========== Page Breadcrumb ============*/
.page-title {
  //  margin-bottom: 1.875rem; //30px;
  .breadcrumb {
    margin-bottom: 0;
  }
  .page-title-wrap > i {
    color: $breadcrumb-font-color;
    vertical-align: 3px;
    margin-right: 10px;
  }
  h2 {
    font-size: $breadcrumb-font-size;
    color: $breadcrumb-font-color;
    display: inline-block;
    text-transform: capitalize;
    margin: 0;
  }
  .rct-creadcrumb {
    color: $breadcrumb-font-color;
    font-size: 14px;
    i {
      font-size: 12px;
      margin: 0 5px;
    }
  }
}
.pointer {
  cursor: pointer;
}

.profile-userpic {
  margin: 0 auto;
  width: 200px;
  height: 200px;
  overflow: hidden;
  border-radius: 50%;
}

.profile-userpic img {
  width: auto;
  height: 100%;
}

.h-max-content {
  height: max-content;
}

body.rtl {
  .MuiAutocomplete-inputRoot[class*="MuiOutlinedInput-root"] .MuiAutocomplete-endAdornment {
    right: 92%;
    position: unset;
  }
  .MuiSelect-iconOutlined {
    right: 92%;
  }
  .MuiChip-root {
    background: none !important;
  }
  legend {
    text-align: right;
  }
  legend span {
    color: red;
  }
  label {
    margin-right: 1.1em;
  }
  .MuiInputBase-root {
    padding-right: 8px !important;
  }
}

// This disable pointer event is because hover on a boostrap styled button was confusing to PO
button:disabled,
button[disabled] {
  pointer-events: none;
}

.selected-flag {
  display: flex !important;
}

.selected-dial-code {
  padding-left: 5px !important;
}

#input-tel {
  height: 40px !important;
  width: 100%;
  width: -moz-available;
  width: -webkit-fill-available;
}

.custom-textfield,
.datepicker-custom-field {
  div {
    height: 40px;
  }
  label {
    margin-top: -4px;
  }
  input {
    margin-top: -5px;
  }
}

.dropdown-select:focus-within {
  max-height: 40px;
  z-index: 100; // Higher than datepicker
}

// TO Add custom error border to dropdown select "react-select"
.dropdown-select.selection-error {
  > div {
    @include errorBorder();
  }
}

.input-error {
  @include errorBorder();
}

.DatePicker:not(:focus-within) {
  position: relative;
  display: inline-block;
  z-index: 1;
}

.DatePicker:focus-within {
  position: relative;
  display: inline-block;
  z-index: 100;
}

.fit-available {
  width: 100%;
  width: -moz-available;
  width: -webkit-fill-available;
}

.grid-gap-10 {
  grid-row-gap: 10px;
}

.flex-basis-available {
  flex-basis: 100%;
  flex-basis: -moz-available;
  flex-basis: -webkit-fill-available;
}

.form-group.required .control-label::after,
.dropdown-select.required [class*="placeholder"]::after {
  content: " *";
  color: red;
}

.logo-loader {
  position: fixed;
  width: 100%;
  min-height: 100%;
  height: 100%;
  left: 0;
  top: 0;
  background: rgba(51, 51, 51, 0.7);
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;
}

input::placeholder {
  text-transform: capitalize;
}

label {
  text-transform: capitalize;
}

.modal-img {
  text-align: center;
}
