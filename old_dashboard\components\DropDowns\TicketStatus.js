/* eslint-disable prettier/prettier */
/* eslint-disable eqeqeq */
/* eslint-disable react/prop-types */
/* eslint-disable no-nested-ternary */
import React from "react";
import PropTypes from "prop-types";
import { useIntl } from "react-intl";
import Select from "react-select";
import { CircularProgress } from "@material-ui/core";
import { ticket_status } from "constants/constants";


export function TicketStatus({
  loading,
  type,
  setSelecteStatus,
  selectedStatus,
  error,

}) {
  const { formatMessage } = useIntl();
 
  const options = ticket_status(formatMessage)
 
  React.useEffect(() => {
    if (!selectedStatus) {
      onClear();
    }
  }, [selectedStatus]);

  const selectInputRef = React.useRef();

  const onClear = () => {
    selectInputRef.current.select.clearValue();
  };
  options.unshift({ value: "all", label: formatMessage({ id: "all" }) });

  return (
    <Select
      className={`dropdown-select ${error ? "selection-error" : ""}`}
      options={options}
      ref={selectInputRef}
      loadOptions={loading}
      defaultValue={options.find((optn) => `${optn.value}` === `${selectedStatus}`)}
      value={options.find((optn) => `${optn.value}` === `${selectedStatus}`)}
      placeholder={formatMessage({ id: type })}
      onChange={(selection) => {
      
          setSelecteStatus(selection?.value);
        
      }}
      noOptionsMessage={() => {
        if (loading) {
          return <CircularProgress />;
        }
        if (!options?.length) return "no data found";
      }}
    />
  );
}
TicketStatus.propTypes = {
  type: PropTypes.string,
  loading: PropTypes.bool,
  selectedStatus: PropTypes.string,
  setSelecteStatus: PropTypes.func,
  error: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
};
