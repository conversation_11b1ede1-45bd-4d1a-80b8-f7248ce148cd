'use client';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Pagination,
  FormControl,
  InputLabel,
  Select,
  Divider,
  useTheme,
  Avatar,
} from '@mui/material';
import {
  MoreVert as MoreVertIcon,
  Edit as EditIcon,
  Print as PrintIcon,
  History as HistoryIcon,
  Assignment as AssignmentIcon,
  Visibility as VisibilityIcon,
  Business as BusinessIcon,
  DirectionsCar as DirectionsCarIcon,
  CalendarToday as CalendarTodayIcon,
  AttachMoney as AttachMoneyIcon,
} from '@mui/icons-material';
import { format } from 'date-fns';
import { ar, enUS } from 'date-fns/locale';
import Link from 'next/link';
import { BusinessBooking } from '../../models/businessBookings.model';

interface BusinessBookingsMobileViewProps {
  bookings: BusinessBooking[];
  page: number;
  limit: number;
  total: number;
  onPageChange: (page: number) => void;
  onLimitChange: (limit: number) => void;
  onAssign?: (booking: BusinessBooking) => void;
  onEdit?: (booking: BusinessBooking) => void;
  onPrint?: (booking: BusinessBooking) => void;
  onViewTimeline?: (booking: BusinessBooking) => void;
}

export default function BusinessBookingsMobileView({
  bookings,
  page,
  limit,
  total,
  onPageChange,
  onLimitChange,
  onAssign,
  onEdit,
  onPrint,
  onViewTimeline,
}: BusinessBookingsMobileViewProps) {
  const { t, i18n } = useTranslation();
  const theme = useTheme();
  const isRTL = i18n.language === 'ar';
  const dateLocale = isRTL ? ar : enUS;
  const { user } = useSelector((state: any) => state.auth);
  const isAlly = user?.ally_id;

  // State for action menu
  const [actionMenuAnchorEl, setActionMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedBooking, setSelectedBooking] = useState<BusinessBooking | null>(null);

  // Handle action menu
  const handleActionMenuOpen = (event: React.MouseEvent<HTMLElement>, booking: BusinessBooking) => {
    setActionMenuAnchorEl(event.currentTarget);
    setSelectedBooking(booking);
  };

  const handleActionMenuClose = () => {
    setActionMenuAnchorEl(null);
    setSelectedBooking(null);
  };

  // Status color mapping
  const getStatusColor = (status: string): 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' => {
    switch (status?.toLowerCase()) {
      case 'confirmed':
        return 'success';
      case 'car_received':
        return 'info';
      case 'invoiced':
        return 'primary';
      case 'cancelled':
        return 'error';
      case 'closed':
        return 'secondary';
      case 'pending':
        return 'warning';
      default:
        return 'default';
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    if (!dateString) return '--';
    try {
      return format(new Date(dateString), 'dd/MM/yyyy HH:mm', { locale: dateLocale });
    } catch {
      return '--';
    }
  };

  // Handle page change
  const handleChangePage = (_event: unknown, newPage: number) => {
    onPageChange(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event: React.ChangeEvent<{ value: unknown }>) => {
    onLimitChange(parseInt(event.target.value as string, 10));
  };

  return (
    <Box sx={{ width: '100%' }}>
      {/* Bookings Cards */}
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mb: 3 }}>
        {bookings.length === 0 ? (
          <Card>
            <CardContent sx={{ textAlign: 'center', py: 4 }}>
              <BusinessIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" color="text.secondary">
                {t('No business bookings found')}
              </Typography>
            </CardContent>
          </Card>
        ) : (
          bookings.map((booking) => (
            <Card
              key={booking.id}
              sx={{
                borderRadius: 2,
                boxShadow: theme.palette.mode === 'dark' 
                  ? '0 2px 8px rgba(0,0,0,0.3)' 
                  : '0 2px 8px rgba(0,0,0,0.1)',
                border: theme.palette.mode === 'dark' 
                  ? '1px solid rgba(255,255,255,0.1)' 
                  : '1px solid rgba(0,0,0,0.05)',
                '&:hover': {
                  boxShadow: theme.palette.mode === 'dark' 
                    ? '0 4px 12px rgba(0,0,0,0.4)' 
                    : '0 4px 12px rgba(0,0,0,0.15)',
                },
              }}
            >
              <CardContent sx={{ p: 2 }}>
                {/* Header */}
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="h6" sx={{ fontWeight: 600, mb: 0.5 }}>
                      <Link
                        href={`/business-bookings/${booking.id}`}
                        style={{ 
                          textDecoration: 'none', 
                          color: theme.palette.primary.main,
                        }}
                      >
                        {booking.bookingNo}
                      </Link>
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {t('ID')}: {booking.id}
                    </Typography>
                  </Box>
                  
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Chip
                      label={t(booking.status?.toUpperCase() || 'UNKNOWN')}
                      color={getStatusColor(booking.status)}
                      size="small"
                      sx={{ fontWeight: 500 }}
                    />
                    <IconButton
                      size="small"
                      onClick={(e) => handleActionMenuOpen(e, booking)}
                    >
                      <MoreVertIcon />
                    </IconButton>
                  </Box>
                </Box>

                {/* Customer Info */}
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                  <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.main' }}>
                    {booking.customerName?.charAt(0) || 'C'}
                  </Avatar>
                  <Box>
                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                      <Link
                        href={`/customers/${booking.userId}`}
                        style={{ 
                          textDecoration: 'none', 
                          color: theme.palette.text.primary,
                        }}
                      >
                        {booking.customerName}
                      </Link>
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {booking.companyName}
                    </Typography>
                  </Box>
                </Box>

                <Divider sx={{ my: 2 }} />

                {/* Details Grid */}
                <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2, mb: 2 }}>
                  {/* Car Info */}
                  <Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mb: 0.5 }}>
                      <DirectionsCarIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
                      <Typography variant="caption" color="text.secondary">
                        {t('car')}
                      </Typography>
                    </Box>
                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                      {(() => {
                        const makeName = isRTL ? booking.arMakeName : booking.enMakeName;
                        const modelName = isRTL ? booking.arModelName : booking.enModelName;
                        return `${makeName || booking.makeName || ''} ${modelName || booking.modelName || ''}`;
                      })()}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {booking.year} • {booking.numberOfCars} {t('cars')}
                    </Typography>
                  </Box>

                  {/* Duration */}
                  <Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mb: 0.5 }}>
                      <CalendarTodayIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
                      <Typography variant="caption" color="text.secondary">
                        {t('Duration')}
                      </Typography>
                    </Box>
                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                      {booking.numberOfMonths} {t('months')}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {booking.pickUpCityName}
                    </Typography>
                  </Box>

                  {/* Pickup Date */}
                  <Box>
                    <Typography variant="caption" color="text.secondary">
                      {t('bookings.list.pickup')}
                    </Typography>
                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                      {formatDate(booking.pickUpDatetime)}
                    </Typography>
                  </Box>

                  {/* Price */}
                  <Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mb: 0.5 }}>
                      <AttachMoneyIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
                      <Typography variant="caption" color="text.secondary">
                        {t('Total Price')}
                      </Typography>
                    </Box>
                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                      {booking.totalBookingPrice 
                        ? `${booking.totalBookingPrice.toLocaleString()} ${t('SAR')}`
                        : '--'
                      }
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {booking.pricePerMonth 
                        ? `${booking.pricePerMonth.toLocaleString()} ${t('SAR')}/month`
                        : '--'
                      }
                    </Typography>
                  </Box>
                </Box>

                {/* Ally Company */}
                {booking.acceptedOffer?.allyCompanyName && (
                  <Box sx={{ mt: 2, p: 1, bgcolor: 'action.hover', borderRadius: 1 }}>
                    <Typography variant="caption" color="text.secondary">
                      {t('bookings.list.allyName')}
                    </Typography>
                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                      <Link
                        href={`/companies/${booking.acceptedOffer.allyCompanyId}`}
                        style={{ 
                          textDecoration: 'none', 
                          color: theme.palette.primary.main,
                        }}
                      >
                        {booking.acceptedOffer.allyCompanyName}
                      </Link>
                    </Typography>
                  </Box>
                )}

                {/* Assignment Status */}
                {!isAlly && (
                  <Box sx={{ mt: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
                    <AssignmentIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
                    <Typography variant="caption" color="text.secondary">
                      {booking.assignedTo ? t('Assigned') : t('Unassigned')}
                    </Typography>
                  </Box>
                )}
              </CardContent>
            </Card>
          ))
        )}
      </Box>

      {/* Pagination */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 3 }}>
        <FormControl size="small" sx={{ minWidth: 120 }}>
          <InputLabel>{t('Per Page')}</InputLabel>
          <Select
            value={limit}
            onChange={handleChangeRowsPerPage}
            label={t('Per Page')}
          >
            <MenuItem value={10}>10</MenuItem>
            <MenuItem value={25}>25</MenuItem>
            <MenuItem value={50}>50</MenuItem>
            <MenuItem value={100}>100</MenuItem>
          </Select>
        </FormControl>

        <Pagination
          count={Math.ceil(total / limit)}
          page={page}
          onChange={handleChangePage}
          color="primary"
          showFirstButton
          showLastButton
          size="small"
        />
      </Box>

      {/* Action Menu */}
      <Menu
        anchorEl={actionMenuAnchorEl}
        open={Boolean(actionMenuAnchorEl)}
        onClose={handleActionMenuClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: isRTL ? 'left' : 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: isRTL ? 'left' : 'right',
        }}
      >
        <MenuItem
          component={Link}
          href={`/business-bookings/${selectedBooking?.id}`}
          onClick={handleActionMenuClose}
        >
          <ListItemIcon>
            <VisibilityIcon />
          </ListItemIcon>
          <ListItemText>{t('common.view')}</ListItemText>
        </MenuItem>

        {onEdit && (
          <MenuItem
            onClick={() => {
              if (selectedBooking) onEdit(selectedBooking);
              handleActionMenuClose();
            }}
          >
            <ListItemIcon>
              <EditIcon />
            </ListItemIcon>
            <ListItemText>{t('common.edit')}</ListItemText>
          </MenuItem>
        )}

        {onPrint && (
          <MenuItem
            onClick={() => {
              if (selectedBooking) onPrint(selectedBooking);
              handleActionMenuClose();
            }}
          >
            <ListItemIcon>
              <PrintIcon />
            </ListItemIcon>
            <ListItemText>{t('print.bookingDetails')}</ListItemText>
          </MenuItem>
        )}

        {onViewTimeline && (
          <MenuItem
            onClick={() => {
              if (selectedBooking) onViewTimeline(selectedBooking);
              handleActionMenuClose();
            }}
          >
            <ListItemIcon>
              <HistoryIcon />
            </ListItemIcon>
            <ListItemText>{t('bookings.timeline.title')}</ListItemText>
          </MenuItem>
        )}

        {onAssign && !isAlly && (
          <MenuItem
            onClick={() => {
              if (selectedBooking) onAssign(selectedBooking);
              handleActionMenuClose();
            }}
          >
            <ListItemIcon>
              <AssignmentIcon />
            </ListItemIcon>
            <ListItemText>{t('assignment.assignBooking')}</ListItemText>
          </MenuItem>
        )}
      </Menu>
    </Box>
  );
}
