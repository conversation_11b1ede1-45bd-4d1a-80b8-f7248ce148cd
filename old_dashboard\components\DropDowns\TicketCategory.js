/* eslint-disable prettier/prettier */
/* eslint-disable eqeqeq */
/* eslint-disable react/prop-types */
/* eslint-disable no-nested-ternary */
import React from "react";
import PropTypes from "prop-types";
import { useIntl } from "react-intl";
import Select from "react-select";
import { CircularProgress } from "@material-ui/core";
import { ticket_category } from "constants/constants";


export function TicketCategory({
  loading,
  type,
  setSelectCategory,
  SelectedCategory,
  error,

}) {
  const { formatMessage } = useIntl();
 
  const options = ticket_category(formatMessage)
 
  React.useEffect(() => {
    if (!SelectedCategory) {
      onClear();
    }
  }, [SelectedCategory]);

  const selectInputRef = React.useRef();

  const onClear = () => {
    selectInputRef.current.select.clearValue();
  };
  options.unshift({ value: "all", label: formatMessage({ id: "all" }) });

  return (
    <Select
      className={`dropdown-select ${error ? "selection-error" : ""}`}
      options={options}
      ref={selectInputRef}
      loadOptions={loading}
      defaultValue={options.find((optn) => `${optn.value}` === `${SelectedCategory}`)}
      value={options.find((optn) => `${optn.value}` === `${SelectedCategory}`)}
      placeholder={formatMessage({ id: type })}
      onChange={(selection) => {
      
          setSelectCategory(selection?.value);
        
      }}
      noOptionsMessage={() => {
        if (loading) {
          return <CircularProgress />;
        }
        if (!options?.length) return "no data found";
      }}
    />
  );
}
TicketCategory.propTypes = {
  type: PropTypes.string,
  loading: PropTypes.bool,
  SelectedCategory: PropTypes.string,
  setSelectCategory: PropTypes.func,
  error: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
};
