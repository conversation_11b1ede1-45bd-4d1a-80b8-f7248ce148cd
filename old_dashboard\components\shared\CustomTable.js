/* eslint-disable prettier/prettier */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable eqeqeq */
/* eslint-disable react/prop-types */
/* eslint-disable prettier/prettier */
/**
 * Custom Table
 */
import React, { useState } from "react";
import PropTypes from "prop-types";
import { FormattedMessage, useIntl } from "react-intl";
import { Box, CircularProgress, Dialog, Modal, Typography } from "@material-ui/core";
import { Alert } from "constants/constants";
import Checkbox from "@material-ui/core/Checkbox";
import TableSortLabel from "@material-ui/core/TableSortLabel";
import CellData from "./CellData";
import SortByComponent from "./SortByComponent";

function Table({
  tableIndex,
  TableCMP,
  rowIndex,
  tableRecords,
  allchecked,
  withcheckbox,
  onSelectAllClick,
  carIds,
  open,
  setOpen,
  actions,
  actionsArgs,
  AssignBooking,
  RefundBooking,
  refetch,
  setSortBy,
  setOrderBy,
  formatMessage,
  indexModal,
  setIndexModal,
  handleClose,
  onSingleCheck,
  locale,
  separatedTableData,
  tableData,
  resetExpanded,
}) {
  const [expandStates, setExpandStates] = useState(new Array(tableRecords?.length).fill(false));

  const handleExpandClick = (idx) => {
    const newExpandStates = [...expandStates];
    newExpandStates[idx] = !newExpandStates[idx];
    setExpandStates(newExpandStates);
  };

  return (
    <div className="table-responsive" style={{ position: "relative" }}>
      <table className="table table-hover" style={{ background: "white" }}>
        <thead>
          <tr data-testid="header-row">
            {!tableIndex && separatedTableData(tableData)?.length > 1 ? (
              <th style={{ width: "1%" }}></th>
            ) : null}
            {withcheckbox && (
              <th style={{ width: "1%" }}>
                <Checkbox
                  checked={allchecked}
                  onChange={onSelectAllClick}
                  inputProps={{ "aria-label": "select all desserts" }}
                />
              </th>
            )}
            {!tableIndex && <th style={{ width: "1%" }}>#</th>}

            {separatedTableData(tableData)[tableIndex]?.map((header, idx) => (
              <th key={JSON.stringify(idx)} align="center" style={{ whiteSpace: "nowrap" }}>
                <div
                  key={header?.headerId}
                  role="button"
                  className={`${header?.withAction ? "pointer" : ""}`}
                  tabIndex={0}
                  onClick={() => {
                    resetExpanded && resetExpanded();
                  }}
                >
                  <span data-testid="capitalized-header" style={{ textTransform: "capitalize" }}>
                    {header?.headerId &&
                      (header.issortable ? (
                        <SortByComponent
                          orderBy={header.orderBy}
                          setOrderBy={setOrderBy}
                          setSortBy={setSortBy}
                        >
                          <FormattedMessage id={header?.headerId || Alert("Missing Header ID")} />
                        </SortByComponent>
                      ) : (
                        <FormattedMessage id={header?.headerId || Alert("Missing Header ID")} />
                      ))}
                  </span>
                </div>
              </th>
            ))}
          </tr>
        </thead>

        <tbody>
          {(rowIndex || rowIndex === 0 ? [tableRecords[rowIndex]] : tableRecords)?.map(
            (record, idx) => {
              const expand = expandStates[idx];

              return (
                <React.Fragment key={idx}>
                  <tr key={JSON.stringify(idx)} data-testid={`data-tr-${idx}`}>
                    {separatedTableData(tableData)[tableIndex]?.length > 1 &&
                    tableIndex + 1 !== separatedTableData(tableData)?.length ? (
                      <td
                        style={{
                          cursor: "pointer",
                          verticalAlign: "middle",
                          width: "1%",
                        }}
                        title={formatMessage({ id: "expand" })}
                        onClick={() => handleExpandClick(idx)}
                      >
                        {expand ? "-" : "+"}
                      </td>
                    ) : null}
                    {withcheckbox && (
                      <td style={{ width: "1%" }}>
                        <Checkbox
                          checked={carIds?.includes(record.id)}
                          onChange={(e) => onSingleCheck(e, idx, record)}
                          inputProps={{ "aria-label": "select all desserts" }}
                        />
                      </td>
                    )}
                    {!tableIndex && <td style={{ whiteSpace: "nowrap" }}> {idx + 1}</td>}

                    {separatedTableData(tableData)[tableIndex].map((data, index) => (
                      <td
                        key={JSON.stringify(index)}
                        align={`${data?.align || ""}`}
                        style={{
                          whiteSpace: "normal",
                        }}
                      >
                        {CellData({
                          data,
                          record,
                          locale,
                          actions,
                          actionsArgs,
                          AssignBooking,
                          RefundBooking,
                          refetch,
                        })}
                        {data?.dataRef === "additionalNotes" &&
                          record?.additionalNotes?.length > 10 && (
                            <div className="d-inline-block">
                              <button
                                style={{ background: "none", border: "none", cursor: "pointer" }}
                                type="button"
                                onClick={() => {
                                  setIndexModal(idx);
                                  setOpen(tableRecords[idx].id == record.id);
                                }}
                              >
                                <span>..</span>
                                <FormattedMessage id="button.more" />
                              </button>
                              <Modal
                                open={open && indexModal == idx}
                                onClose={handleClose}
                                aria-labelledby="modal-modal-title"
                                aria-describedby="modal-modal-description"
                              >
                                <Box className="custom-popup px-4 py-2 position-relative">
                                  <i
                                    style={{ cursor: "pointer" }}
                                    onClick={() => setOpen(false)}
                                    className="ti-close"
                                  ></i>
                                  <h4 style={{ textDecoration: "underline", fontWeight: "bold" }}>
                                    <FormattedMessage id="Additional notes" />
                                  </h4>
                                  <p className="m-0" style={{ fontSize: "18px" }}>
                                    {record?.additionalNotes}
                                  </p>
                                </Box>
                              </Modal>
                            </div>
                          )}
                      </td>
                    ))}
                  </tr>
                  {expand ? (
                    <tr>
                      <td colSpan={separatedTableData(tableData)[tableIndex].length + 2}>
                        <TableCMP
                          {...{
                            tableIndex: Number(tableIndex) + 1,
                            TableCMP: Table,
                            tableRecords,
                            allchecked,
                            withcheckbox,
                            onSelectAllClick,
                            carIds,
                            open,
                            setOpen,
                            actions,
                            actionsArgs,
                            AssignBooking,
                            RefundBooking,
                            refetch,
                            setSortBy,
                            setOrderBy,
                            formatMessage,
                            indexModal,
                            setIndexModal,
                            handleClose,
                            onSingleCheck,
                            locale,
                            separatedTableData,
                            tableData,
                            rowIndex: idx,
                            resetExpanded: () => {
                              setExpandStates([]);
                            },
                          }}
                        />
                      </td>
                    </tr>
                  ) : null}
                </React.Fragment>
              );
            },
          )}
        </tbody>
      </table>
    </div>
  );
}

function CustomTable({
  tableData,
  tableRecords,
  loading,
  setCarIds,
  carIds,
  withcheckbox = false,
  setAllChecked,
  allchecked,
  actions,
  actionsArgs,
  AssignBooking,
  RefundBooking,
  refetch,
  setSortBy,
  setOrderBy,
}) {
  const { locale, formatMessage } = useIntl();
  const [open, setOpen] = useState(false);
  const [indexModal, setIndexModal] = useState();

  const handleClose = () => setOpen(false);

  const onSelectAllClick = (e) => {
    if (e.target.checked) {
      setAllChecked(true);
      const ids = [];
      tableRecords.map((record) => ids.push(record.id));
      setCarIds(ids);
    } else {
      setAllChecked(false);
      setCarIds([]);
    }
  };
  const onSingleCheck = (e, index, record) => {
    const ids = [...carIds];
    if (e.target.checked) {
      setCarIds([...carIds, record.id]);
    } else {
      const filteredids = ids.filter((id) => id != record.id);
      setCarIds(filteredids);
    }
  };
  const separatedTableData = (data) => {
    const itemInRow = 12;
    if (data?.length) {
      if (data.length) return [data];
      // if (data.length < itemInRow) return [data];
      // let _data = [];
      // for (let i = 0; i < Math.ceil(data.length / itemInRow); i++) {
      //   _data.push([...data].slice(i * itemInRow, (i + 1) * itemInRow));
      // }
      // return _data;
    } else {
      return [];
    }
  };

  return (
    <>
      {tableRecords && tableRecords.length ? (
        <Table
          {...{
            tableIndex: 0,
            TableCMP: Table,
            tableRecords,
            allchecked,
            withcheckbox,
            onSelectAllClick,
            carIds,
            open,
            setOpen,
            actions,
            actionsArgs,
            AssignBooking,
            RefundBooking,
            refetch,
            setSortBy,
            setOrderBy,
            formatMessage,
            indexModal,
            setIndexModal,
            handleClose,
            onSingleCheck,
            locale,
            separatedTableData,
            tableData,
          }}
        />
      ) : (
        <div className="table-responsive" style={{ position: "relative" }}>
          <div className="text-center mt-3 mb-4">
            <FormattedMessage id="No data found" />
          </div>
        </div>
      )}
      {loading && (
        <div
          className="d-flex justify-content-center align-items-center"
          style={{
            zIndex: "999",
            position: "fixed",
            top: 0,
            width: "calc(100vw - 140px)",
            height: "100vh",
            left: locale == "ar" ? 0 : "",
            right: locale == "ar" ? "" : 0,
          }}
        >
          <CircularProgress />
        </div>
      )}
    </>
  );
}

CustomTable.propTypes = {
  tableData: PropTypes.array,
  tableRecords: PropTypes.array,
  actionsArgs: PropTypes.array,
  actions: PropTypes.any,
  loading: PropTypes.bool,
};

export default CustomTable;
