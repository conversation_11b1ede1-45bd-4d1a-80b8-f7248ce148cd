.rct-sidebar .site-logo {
  @media screen and (min-width: 901px) {
    padding-top: 20px;
  }

  @media screen and (max-width: 900px) {
    padding-top: 20px;
  }
}

@media screen and (min-width: 901px) {
  .MuiToolbar-regular {
    min-height: 48px !important;
  }
}

.rct-footer {
  margin-top: 20px;
}

.navbar-right {
  display: flex;
  align-items: center;

  li {
    display: block !important;
  }
}

.Mui-error::after,
.Mui-error+p {
  // display: none;
}

@media screen and (max-width: 900px) {
  .grid-insurance-cars {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-column-gap: 10px;
    width: 100%;
    margin: 0;
  }

  .ecom-dashboard-wrapper>.row {
    display: block !important;

    .w-50,
    .w-60 {
      width: 100% !important;
    }
  }
}

.add-edit-coupons .invisible {
  display: none;
}

.add-edit-coupons p.visible {
  margin-bottom: 0 !important;
}

.rct-sidebar .rct-sidebar-nav .rct-mainMenu>li:not(.list-item) a {
  padding: 1rem !important;
}

.MuiTableSortLabel-icon {
  opacity: 0.4 !important;
}

.MuiCollapse-wrapperInner a {
  width: 100%;
}

.swal-text {
  text-align: start;
}

.swal-footer {
  text-align: center;
}

.table {
  background-color: #fff !important;
}

.table th,
.table td {
  white-space: nowrap !important;
  padding: 0.4rem !important;
  font-size: 13px !important;

  p {
    margin-bottom: 0 !important;
  }

}

.MuiTab-wrapper .MuiBadge-root {
  margin: 0 !important;
}

.MuiTab-labelIcon {
  padding: 7px 14px !important;
  min-height: 50px !important;
}

.MuiTab-root {
  min-width: max-content !important;
}

.rct-page-content {
  padding: 12px 24px !important;

  @media (max-width: 960px) {
    padding-right: 5px !important;
    padding-left: 5px !important;
  }
}

.page-title {
  margin: 0px 5px !important;
  margin-bottom: 10px !important;
  flex-wrap: wrap;

  >div {
    flex-wrap: wrap;
  }

  button {
    margin-top: -5px;
  }
}

@media (min-width: 600px) {
  .MuiToolbar-regular {
    min-height: 38px !important;
  }
}

.humburger,
.header-icon {
  border: none !important;
}

.clients-wrapper {
  .MuiTypography-body1 {
    padding-top: 0px !important;
    margin-top: 0px !important;
  }

  >div:nth-child(2) {
    position: relative;
    z-index: 1;
  }
}

.language-icon {
  img {
    transform: translateY(-2px);
    -webkit-transform: translateY(-2px);
    -moz-transform: translateY(-2px);
    -ms-transform: translateY(-2px);
    -o-transform: translateY(-2px);
    margin: -3px;
  }
}

.MuiTab-wrapper {
  font-size: 0.85rem !important
}

.page-title .breadcrumb {
  font-size: 14px !important;
  padding: 0px !important;
  scale: 0.9 !important;
  margin: 5px 0px !important;
}

.page-title-wrap h2 {
  font-size: 1.6rem !important;
  width: max-content;
}

// .MuiTabs-indicator{
//   display: none;
// }

// table a{
//   text-decoration: underline;
// }
.rct-block .rct-block-content {
  padding: 7px !important;
}

.badge:not(.badge-xs) {
  padding: 5px 10px 5px 10px !important;
}

span.MuiSwitch-root {
  scale: 0.7;
  margin: -15px;
}

.MuiTab-textColorPrimary.Mui-selected {
  font-weight: bold;
}

.MuiTabs-indicator {
  display: none;
}

.makes-select {
  display: table !important;
}

@media (max-width: 768px) {
  #side-bar-wrapper {
    >div {
      >div:first-child {
        padding-top: 50px;
      }
    }

    li.MuiListItem-gutters {

      // padding: 0 !important;
      span {
        font-size: 0.9rem;
      }
    }
  }
}

@media (max-width: 1200px) {
  .MuiToolbar-regular {
    min-height: 56px !important;
  }
}

#side-bar-wrapper {
  >div {
    >div:first-child {
      transform: translate(0) !important;
      -webkit-transform: translate(0) !important;
      -moz-transform: translate(0) !important;
      -ms-transform: translate(0) !important;
      -o-transform: translate(0) !important;

    }
  }

}

// }

#side-bar-wrapper-collapsed {
  @media (min-width: 768px) {
    >div {
      >div:first-child {
        transform: translateX(0) !important;
        -webkit-transform: translateX(0) !important;
        -moz-transform: translateX(0) !important;
        -ms-transform: translateX(0) !important;
        -o-transform: translateX(0) !important;

        .MuiCollapse-root.MuiCollapse-entered {
          display: none;
        }
      }

      >div:last-child {
        inset: 0px 0px 0px 80px !important;
      }
    }
  }

  @media (max-width: 767px) {
    &>div>div:first-child .MuiCollapse-entered {
      display: none;
    }

    .site-logo {
      padding: 0.1em 0.2rem !important;
    }
  }

  .rct-sidebar-content {

    // .site-logo,
    // p {
    //   opacity: 0;
    //   visibility: hidden;
    // }

    .rct-mainMenu {
      .menu {
        display: none;
      }
    }
  }

  .rct-sidebar-content,
  .rct-sidebar {
    width: min-content;
  }

  .rct-mainMenu {
    li {
      text-align: center !important;
    }
  }

  .site-logo {
    padding: 0.1em 0.8rem;
  }

}

body {
  #side-bar-wrapper-collapsed {
    >div {
      >div:nth-child(2) {
        display: none;
      }
    }
  }
}

body {
  #side-bar-wrapper-collapsed {
    >div {
      >div:nth-child(2) {
        display: none;
      }
    }
  }
}

body {
  #side-bar-wrapper-collapsed {
    >div {
      >div:nth-child(2) {
        display: none;
      }
    }
  }
}

body.rtl {
  #side-bar-wrapper-collapsed {
    >div {
      >div:nth-child(2) {
        background-color: transparent !important;
      }

      @media (min-width: 768px) {
        >div:last-child {
          inset: 0px 80px 0 0px !important;
        }
      }

      @media (max-width: 767px) {
        >div:last-child {
          inset: 0px 60px 0 0px !important;
        }
      }

    }

    .rct-scroll>div:first-child {
      margin-left: -18px !important;
    }
  }
}

body {
  #side-bar-wrapper-collapsed {
    >div {

      @media (max-width: 767px) {
        >div:last-child {
          inset: 0px 0px 0 60px !important;
        }
      }

    }

  }
}

.intl-tel-input {
  height: min-content;
}

.MuiInputBase-input.Mui-disabled {
  color: rgb(84, 84, 84) !important;
}

@media (max-width: 768px) {
  .mobile-flex-column-reverse {
    flex-direction: column-reverse !important;
  }
}