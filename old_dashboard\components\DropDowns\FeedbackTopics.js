/* eslint-disable prettier/prettier */
/* eslint-disable eqeqeq */
/* eslint-disable react/prop-types */
/* eslint-disable no-nested-ternary */
import React from "react";
import PropTypes from "prop-types";
import { useIntl } from "react-intl";
import Select from "react-select";
import { CircularProgress } from "@material-ui/core";
import { useQuery } from "@apollo/client";
import { FeedbackTopics } from "gql/queries/feedbackTopics.gql";

export function FeedbackTopicsDropDown({
  loading,
  type,
  setSelecteTopic,
  selectedTopic,
  error,
  feedbackType,
  feedbackAbout,
}) {
  const { formatMessage } = useIntl();
  const { data: topics } = useQuery(FeedbackTopics, {
    variables: {
      feedbackType: feedbackType == "all" ? undefined : feedbackType,
      feedbackAbout: feedbackAbout == "all" ? undefined : feedbackAbout,
    },
  });
  //   [...new Set(data?.privileges?.map((priv) => priv.name.split(".")[0]))]
  const options =
    topics?.feedbackTopics
      .filter((obj, index, self) => index === self.findIndex((o) => o.name === obj.name))
      ?.map((item) => ({
        label: item.name,
        value: item.id,
      })) || [];

  React.useEffect(() => {
    if (!selectedTopic) {
      onClear();
    }
  }, [selectedTopic]);

  const selectInputRef = React.useRef();

  const onClear = () => {
    selectInputRef.current.select.clearValue();
  };

  return (
    <Select
      className={`dropdown-select ${error ? "selection-error" : ""}`}
      options={options}
      isClearable
      ref={selectInputRef}
      loadOptions={loading}
      defaultValue={options.find((optn) => `${optn.value}` === `${selectedTopic}`)}
      value={options.find((optn) => `${optn.value}` === `${selectedTopic}`)}
      placeholder={formatMessage({ id: type })}
      onChange={(selection) => {
        setSelecteTopic(selection?.value);
      }}
      noOptionsMessage={() => {
        if (loading) {
          return <CircularProgress />;
        }
        if (!options?.length) return "no data found";
      }}
    />
  );
}
FeedbackTopicsDropDown.propTypes = {
  type: PropTypes.string,
  loading: PropTypes.bool,
  selectedTopic: PropTypes.string,
  setSelecteTopic: PropTypes.func,
  error: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
};
