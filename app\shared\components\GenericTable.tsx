'use client';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TableSortLabel,
  Paper,
  Chip,
  IconButton,
  Typography,
  useTheme,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  CircularProgress,
  Skeleton,
} from '@mui/material';
import { MoreVert as MoreVertIcon, Visibility as VisibilityIcon } from '@mui/icons-material';
import { visuallyHidden } from '@mui/utils';
import Link from 'next/link';

// Generic column interface
export interface GenericTableColumn {
  id: string;
  label: string;
  minWidth?: number;
  align?: 'right' | 'left' | 'center';
  format?: (value: any, row: any) => React.ReactNode;
  sortable?: boolean;
}

// Generic action interface
export interface GenericTableAction {
  id: string;
  label: string;
  icon: React.ReactNode;
  onClick: (row: any) => void;
  color?: 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success';
  show?: (row: any) => boolean;
}

interface GenericTableProps {
  data: any[];
  columns: GenericTableColumn[];
  actions?: GenericTableAction[];
  page: number;
  limit: number;
  total: number;
  onPageChange: (page: number) => void;
  onLimitChange: (limit: number) => void;
  onSortChange?: (field: string, direction: 'asc' | 'desc') => void;
  orderBy?: string;
  sortBy?: string;
  loading?: boolean;
  emptyMessage?: string;
  detailsPath?: string; // Base path for details page (e.g., '/business-bookings')
  idField?: string; // Field to use for ID (default: 'id')
}

export default function GenericTable({
  data,
  columns,
  actions = [],
  page,
  limit,
  total,
  onPageChange,
  onLimitChange,
  onSortChange,
  orderBy = '',
  sortBy = 'desc',
  loading = false,
  emptyMessage,
  detailsPath,
  idField = 'id',
}: GenericTableProps) {
  const { t, i18n } = useTranslation();
  const theme = useTheme();
  const isRTL = i18n.language === 'ar';

  // State for action menu
  const [actionMenuAnchorEl, setActionMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedRow, setSelectedRow] = useState<any>(null);

  // Handle action menu
  const handleActionMenuOpen = (event: React.MouseEvent<HTMLElement>, row: any) => {
    setActionMenuAnchorEl(event.currentTarget);
    setSelectedRow(row);
  };

  const handleActionMenuClose = () => {
    setActionMenuAnchorEl(null);
    setSelectedRow(null);
  };

  // Handle sort
  const handleSort = (columnId: string) => {
    if (!onSortChange) return;

    const isAsc = orderBy === columnId && sortBy === 'asc';
    onSortChange(columnId, isAsc ? 'desc' : 'asc');
  };

  // Handle page change
  const handleChangePage = (_event: unknown, newPage: number) => {
    onPageChange(newPage + 1); // Convert from 0-based to 1-based
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    onLimitChange(parseInt(event.target.value, 10));
  };

  // Get visible actions for a row
  const getVisibleActions = (row: any) => {
    return actions.filter((action) => !action.show || action.show(row));
  };

  // Loading skeleton
  if (loading && data.length === 0) {
    return (
      <Paper sx={{ width: '100%', overflow: 'hidden' }}>
        <TableContainer>
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                {columns.map((column) => (
                  <TableCell
                    key={column.id}
                    align={column.align}
                    style={{ minWidth: column.minWidth }}
                  >
                    <Skeleton variant="text" width="80%" />
                  </TableCell>
                ))}
                {actions.length > 0 && (
                  <TableCell align="center">
                    <Skeleton variant="text" width="60%" />
                  </TableCell>
                )}
              </TableRow>
            </TableHead>
            <TableBody>
              {Array.from(new Array(limit)).map((_, index) => (
                <TableRow key={index}>
                  {columns.map((column) => (
                    <TableCell key={column.id}>
                      <Skeleton variant="text" />
                    </TableCell>
                  ))}
                  {actions.length > 0 && (
                    <TableCell>
                      <Skeleton variant="circular" width={32} height={32} />
                    </TableCell>
                  )}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>
    );
  }

  return (
    <Paper sx={{ width: '100%', overflow: 'hidden' }}>
      <TableContainer sx={{ maxHeight: '70vh' }}>
        <Table stickyHeader>
          <TableHead>
            <TableRow>
              {columns.map((column) => (
                <TableCell
                  key={column.id}
                  align={column.align}
                  style={{ minWidth: column.minWidth }}
                  sx={{
                    backgroundColor: theme.palette.mode === 'dark' ? '#2a2a2a' : '#f5f5f5',
                    fontWeight: 600,
                    fontSize: '0.875rem',
                    borderBottom: `2px solid ${theme.palette.divider}`,
                    whiteSpace: 'nowrap',
                    height: '48px',
                    padding: '8px 16px',
                  }}
                >
                  {column.sortable && onSortChange ? (
                    <TableSortLabel
                      active={orderBy === column.id}
                      direction={orderBy === column.id ? (sortBy as 'asc' | 'desc') : 'asc'}
                      onClick={() => handleSort(column.id)}
                      sx={{
                        '& .MuiTableSortLabel-icon': {
                          color: `${theme.palette.primary.main} !important`,
                        },
                      }}
                    >
                      {column.label}
                      {orderBy === column.id ? (
                        <Box component="span" sx={visuallyHidden}>
                          {sortBy === 'desc' ? 'sorted descending' : 'sorted ascending'}
                        </Box>
                      ) : null}
                    </TableSortLabel>
                  ) : (
                    column.label
                  )}
                </TableCell>
              ))}
              {actions.length > 0 && (
                <TableCell
                  align="center"
                  sx={{
                    backgroundColor: theme.palette.mode === 'dark' ? '#2a2a2a' : '#f5f5f5',
                    fontWeight: 600,
                    fontSize: '0.875rem',
                    borderBottom: `2px solid ${theme.palette.divider}`,
                    whiteSpace: 'nowrap',
                    height: '48px',
                    padding: '8px 16px',
                  }}
                >
                  {t('common.actions')}
                </TableCell>
              )}
            </TableRow>
          </TableHead>
          <TableBody>
            {data.length === 0 ? (
              <TableRow>
                <TableCell colSpan={columns.length + (actions.length > 0 ? 1 : 0)} align="center">
                  <Box sx={{ py: 4 }}>
                    <Typography variant="body1" color="text.secondary">
                      {emptyMessage || t('No data available')}
                    </Typography>
                  </Box>
                </TableCell>
              </TableRow>
            ) : (
              data.map((row, index) => (
                <TableRow
                  hover
                  key={row[idField] || index}
                  sx={{
                    '&:hover': {
                      backgroundColor:
                        theme.palette.mode === 'dark'
                          ? 'rgba(255,255,255,0.05)'
                          : 'rgba(0,0,0,0.04)',
                    },
                  }}
                >
                  {columns.map((column) => {
                    const value = row[column.id];
                    return (
                      <TableCell
                        key={column.id}
                        align={column.align}
                        sx={{
                          padding: '12px 16px',
                          borderBottom: `1px solid ${theme.palette.divider}`,
                          whiteSpace: 'nowrap',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          maxWidth: column.minWidth || 200,
                          verticalAlign: 'middle',
                          height: 'auto',
                          '& > *': {
                            whiteSpace: 'nowrap',
                          },
                        }}
                      >
                        {column.format ? column.format(value, row) : value || '--'}
                      </TableCell>
                    );
                  })}
                  {actions.length > 0 && (
                    <TableCell
                      align="center"
                      sx={{
                        padding: '12px 16px',
                        whiteSpace: 'nowrap',
                        verticalAlign: 'middle',
                      }}
                    >
                      <Box sx={{ display: 'flex', gap: 0.5, justifyContent: 'center' }}>
                        {detailsPath && (
                          <IconButton
                            component={Link}
                            href={`${detailsPath}/${row[idField]}`}
                            size="small"
                            color="primary"
                            sx={{
                              backgroundColor: 'rgba(25, 118, 210, 0.04)',
                              '&:hover': {
                                backgroundColor: 'rgba(25, 118, 210, 0.08)',
                              },
                            }}
                          >
                            <VisibilityIcon fontSize="small" />
                          </IconButton>
                        )}

                        {getVisibleActions(row).length > 0 && (
                          <IconButton
                            size="small"
                            onClick={(e) => handleActionMenuOpen(e, row)}
                            sx={{
                              backgroundColor: 'rgba(0, 0, 0, 0.04)',
                              '&:hover': {
                                backgroundColor: 'rgba(0, 0, 0, 0.08)',
                              },
                            }}
                          >
                            <MoreVertIcon fontSize="small" />
                          </IconButton>
                        )}
                      </Box>
                    </TableCell>
                  )}
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Pagination */}
      <TablePagination
        rowsPerPageOptions={[10, 25, 50, 100]}
        component="div"
        count={total}
        rowsPerPage={limit}
        page={page - 1} // Convert from 1-based to 0-based
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
        labelRowsPerPage={t('Rows per page')}
        labelDisplayedRows={({ from, to, count }) =>
          `${from}-${to} ${t('of')} ${count !== -1 ? count : `${t('more than')} ${to}`}`
        }
        sx={{
          borderTop: `1px solid ${theme.palette.divider}`,
          backgroundColor: theme.palette.mode === 'dark' ? '#2a2a2a' : '#f5f5f5',
        }}
      />

      {/* Action Menu */}
      <Menu
        anchorEl={actionMenuAnchorEl}
        open={Boolean(actionMenuAnchorEl)}
        onClose={handleActionMenuClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: isRTL ? 'left' : 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: isRTL ? 'left' : 'right',
        }}
      >
        {selectedRow &&
          getVisibleActions(selectedRow).map((action) => (
            <MenuItem
              key={action.id}
              onClick={() => {
                action.onClick(selectedRow);
                handleActionMenuClose();
              }}
            >
              <ListItemIcon sx={{ color: `${action.color || 'primary'}.main` }}>
                {action.icon}
              </ListItemIcon>
              <ListItemText>{action.label}</ListItemText>
            </MenuItem>
          ))}
      </Menu>
    </Paper>
  );
}
