import { gql } from '@apollo/client';

export const GetMakes = gql`
  query GetMakesQuery($page: Int, $limit: Int) {
    makes(page: $page, limit: $limit) {
      collection {
        id
        arName
        enName
        logo
        status
      }
      metadata {
        currentPage
        totalCount
      }
    }
  }
`;

export const GET_ALL_MAKES = gql`
  query GetAllMakes {
    makes(limit: 1000) {
      collection {
        id
        arName
        enName
        name
        logo
        status
      }
    }
  }
`;
