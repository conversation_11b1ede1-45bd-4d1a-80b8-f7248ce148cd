import React from "react";
import PropTypes from "prop-types";
import { useIntl } from "react-intl";
import { useQuery } from "@apollo/client";
import Select from "react-select";
import { CircularProgress } from "@material-ui/core";
import  VehicleTypes from "gql/queries/GetAllVehicleType.gql";

export default function VehicleTypeDropDown({
  loading,
  selectedVehicle,
  label,
  setSelectedVehicle,
  error,
  multi,
  valueAttribute,
  ...props
}) {
  const { locale, formatMessage } = useIntl();

  const { data: vehicleRes, loading: gettingvehicles } = useQuery(VehicleTypes, {
    variables:{orderBy:`${locale}_name`,sortBy:'asc'}
  });

  const options =
  vehicleRes?.vehicleTypes?.map((x) => ({
      value: x[valueAttribute || "enName"],
      label: x[`${locale}Name`],
    })) || [];

  React.useEffect(() => {
    if (!selectedVehicle) {
      onClear();
    }
  }, [selectedVehicle]);

  const selectInputRef = React.useRef();

  const onClear = () => {
    selectInputRef.current.select.clearValue();
  };
  if(multi){

  options.unshift({value:"all",label:formatMessage({ id: "all" })})
}

  return (
    <Select
      className={`dropdown-select ${error ? "selection-error" : ""}`}
      isMulti={multi}
      isClearable
      options={options}
      ref={selectInputRef}
      loadOptions={gettingvehicles || loading}
      defaultValue={options.find((optn) => `${optn.value}` === `${selectedVehicle}`)}
      value= { multi ? 
        options?.filter((optn) => selectedVehicle?.includes(+optn.value))
        : options.find((optn) => `${optn.value}` === `${selectedVehicle}`)}
      placeholder={formatMessage({ id: label ? label :  "vehicle.type" })}
      // onChange={(selection) => {
      //   console.log(selection,"selection")
      //   setSelectedVehicle(selection?.value == "all" ? selection?.value : +selection?.value)
      // }}
      onChange={(selection) => {
        if (multi) {
          const extraids = [];
          if (selection == null && multi) {
            setSelectedVehicle();
            return;
          }
          if (selection[0].value == "all" || selection[selection.length - 1].value == "all") {
            options.map(
              (onselectoion) => onselectoion.value != "all" && extraids.push(+onselectoion?.value),
            );
          }
          selection?.map((onselectoion) => extraids.push(+onselectoion?.value));
          if (extraids.length) {
            const extras = extraids.filter((id) => !isNaN(id));
            setSelectedVehicle([...extras]);
          } else {
            setSelectedVehicle([]);
          }
        } else {
          if (selection?.value == "all") {
            setSelectedVehicle("null");
            return;
          }
          setSelectedVehicle(+selection?.value);
        }
      }}
      noOptionsMessage={() => {
        if (gettingvehicles) {
          return <CircularProgress />;
        }
        if (!options?.length) return "no data found";
      }}
      {...props}
    />
  );
}
VehicleTypeDropDown.propTypes = {
  valueAttribute: PropTypes.string,
  loading: PropTypes.bool,
  selectedVehicle: PropTypes.string,
  setSelectedVehicle: PropTypes.func,
  error: PropTypes.oneOfType([PropTypes.bool, PropTypes.string]),
};
