import React from "react";
import PropTypes from "prop-types";
import { useIntl } from "react-intl";
import { allyClassOptions } from "constants/constants";
import Select from "react-select";
import { CircularProgress } from "@material-ui/core";

export default function AllyClassDropDown({
  loading,
  setSelectedClass,
  selectedClass,
  error,
}) {
  const { formatMessage } = useIntl();


  React.useEffect(() => {
    if (!selectedClass) {
      onClear();
    }
  }, [selectedClass]);

  const selectInputRef = React.useRef();

  const onClear = () => {
    selectInputRef.current.select.clearValue();
  };

  return (
    <Select
      className={`dropdown-select ${error ? "selection-error" : ""}`}
      options={allyClassOptions(formatMessage)}
      isClearable
      ref={selectInputRef}
      loadOptions={loading}
      defaultValue={allyClassOptions(formatMessage).find((optn) => `${optn.value}` === `${selectedClass}`)}
      value={allyClassOptions(formatMessage).find((optn) => `${optn.value}` === `${selectedClass}`)}
      placeholder={`${formatMessage({ id: "class" })} `}
      onChange={(selection) => {
        setSelectedClass(selection?.value);
      }}
     
    />

  );
}
AllyClassDropDown.propTypes = {
  loading: PropTypes.bool,
  selectedClass: PropTypes.string,
  setSelectedClass: PropTypes.func,
  error: PropTypes.oneOfType([PropTypes.bool, PropTypes.string]),
};
