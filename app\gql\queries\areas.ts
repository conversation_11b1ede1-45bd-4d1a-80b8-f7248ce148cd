import { gql } from '@apollo/client';
export const AREAS = gql`
  query areas($allyCompanyId: ID, $filter: String, $ids: [ID!], $name: String) {
    areas(allyCompanyId: $allyCompanyId, filter: $filter, ids: $ids, name: $name) {
      arName

      centerLat
      centerLng
      enName
      id
      isAirport
      name
      order
      timezone
    }
  }
`;

export const GET_ALL_AREAS = gql`
  query GetAllAreas {
    areas {
      id
      arName
      enName
      centerLat
      centerLng

      isAirport
      name
    }
  }
`;

export const GET_AREA_BY_ID = gql`
  query GetAreaById($id: ID!) {
    area(id: $id) {
      id
      arName
      enName
      centerLat
      centerLng
      isActive
      isAirport
      name
      createdAt
      updatedAt
    }
  }
`;

export const GET_CITIES_FOR_BOOKING = gql`
  query GetCitiesForBooking {
    areas {
      id
      arName
      enName
      centerLat
      centerLng
      isAirport
      name
    }
  }
`;

export const GET_AREA_BY_COORDINATES = gql`
  query GetAreaByCoordinates($lat: Float!, $lng: Float!) {
    area(lat: $lat, lng: $lng) {
      id
      arName
      enName
    }
  }
`;
