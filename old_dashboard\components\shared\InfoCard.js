/* eslint-disable prettier/prettier */
/* eslint-disable react/button-has-type */
/* eslint-disable jsx-a11y/control-has-associated-label */
/* eslint-disable react/prop-types */
/**
 * Info Card
 */

import React from "react";
import PropTypes from "prop-types";
import IntlMessages from "util/IntlMessages";
import ListedInformation from "components/shared/ListedInformation";
import { FormattedMessage } from "react-intl";
import RctCollapsibleCard from "components/RctCollapsibleCard";
import YakeenVerification from "routes/components/Yakeen";

function InfoCard({
  data,
  titleId,
  fullwidth,
  features,
  branches,
  inbookingDetails,
  areas,
  allyCompanies,
  rejectReasones,
  canSendToAlly,
  ExtensionCard = false,
  ExtensionId,
  sendToAlly,
  RentalContract,
  notes,
  plans,
  closingReasons,
  cancelledReason,
  AllyrejectReason,
  yakeenTriesLeft,
  isYakeenVerified,
  userId,
  refetch,
  replies
}) {
  return (
    <div className={`col-sm-12 col-md-${fullwidth ? "12" : "6"} ${inbookingDetails ? "p-0" : ""}`}>
      <RctCollapsibleCard
        colClasses="col-sm-12 col-md-12 col-lg-12 w-xs-full p-0"
        heading={
          <div className="d-flex" style={{ gap: "7px", alignItems: "center" }}>
            <IntlMessages id={titleId} />
            {Boolean(userId && refetch) && (
              <YakeenVerification
                {...{
                  isYakeenVerified,
                  yakeenTriesLeft,
                  userId,
                  refetch,
                }}
              />
            )}
          </div>
        }
        collapsible
        fullBlock
        customClasses="overflow-hidden"
      >
        <ListedInformation
          data={data}
          features={features}
          branches={branches}
          areas={areas}
          allyCompanies={allyCompanies}
          rejectReasones={rejectReasones}
          RentalContract={RentalContract}
          notes={notes}
          plans={plans}
          closingReasons={closingReasons}
          cancelledReason={cancelledReason}
          AllyrejectReason={AllyrejectReason}
          replies={replies}
        />
        {canSendToAlly && ExtensionCard ? (
          <button className="btn btn-primary w-100" onClick={() => sendToAlly(ExtensionId)}>
            <FormattedMessage id="Resend to ally" />
          </button>
        ) : null}
      </RctCollapsibleCard>
    </div>
  );
}

InfoCard.propTypes = {
  fullwidth: PropTypes.bool,
  titleId: PropTypes.string,
  data: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
};

export default InfoCard;
