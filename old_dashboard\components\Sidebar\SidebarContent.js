/* eslint-disable prettier/prettier */
/**
 * Sidebar Content
 */
import React, { Component } from "react";
import PropTypes from "prop-types";
import List from "@material-ui/core/List";
import { withRouter } from "react-router-dom";
import { connect } from "react-redux";
import { onToggleMenu } from "actions";
import { userCan } from "functions/userCan";
import NavMenuItem from "./NavMenuItem";
import "./style.css";
const sideBarContent = [
  {
    permissions: ["rentals.list"],
    menu_icon: "ti-stats-up",
    path: "/cw/dashboard/Statistics",
    new_item: false,
    menu_title: "sidebar.statistics",
  },
  {
    permissions: [
      "rentals.list",
      "rentals.cancel",
      "rentals.update_status",
      "rentals.update",
      "rentals.create",
    ],
    menu_icon: "ti-briefcase",
    path: "/cw/dashboard/bookings",
    new_item: false,
    menu_title: "sidebar.bookings",
  },
  {
    parent: true,
    title: "Carwah Business",
    menu_icon: "ti-folder",
    children: [
      {
        path: "/cw/dashboard/businessBookings",
        new_item: false,
        menu_title: "businessBookings",
        permissions: [
          "business_rentals.list",
          "business_rentals.close",
          "business_rentals.view",
          "business_rentals.update",
        ],
      },
      {
        path: "/cw/dashboard/businessrequests",
        new_item: false,
        menu_title: "sidebar.businessrequests",
        permissions: [
          "business_requests.list",
          "business_requests.view",
          "business_requests.offer",
          "business_requests.create",
          "business_requests.update",
        ],
      },
    ],
  },
  {
    permissions: [
      "users.list",
      "users.create",
      "users.show",
      "users.update",
      "users.activation",
      "users.delete",
    ],
    menu_icon: "ti-user",
    path: "/cw/dashboard/customers",
    new_item: false,
    menu_title: "sidebar.customers",
  },
  {
    parent: true,
    title: "Allies",
    menu_icon: "ti-shield",
    children: [
      {
        permissions: [
          "ally_companies.create",
          "ally_companies.update",
          "ally_companies.activation",
        ],
        path: "/cw/dashboard/companies",
        new_item: false,
        menu_title: "sidebar.companies",
      },
      {
        permissions: ["branches.create", "branches.update", "branches.activation", "no.permission"],
        path: "/cw/dashboard/branches",
        new_item: false,
        menu_title: "sidebar.branches",
      },
      {
        permissions: [
          "ally_companies.create",
          "ally_companies.update",
          "ally_companies.activation",
        ],
        path: "/cw/dashboard/managers",
        new_item: false,
        menu_title: "sidebar.managers",
      },
      {
        permissions: [
          "users.list",
          "users.create",
          "users.show",
          "users.update",
          "users.activation",
          "users.delete",
        ],
        path: "/cw/dashboard/allies-rates",
        new_item: false,
        menu_title: "sidebar.alliesRates",
      },
    ],
  },
  {
    parent: true,
    title: "Cars",
    menu_icon: "ti-car",
    children: [
      {
        permissions: ["cars.create", "cars.update", "cars.activation"],

        path: "/cw/dashboard/cars",
        new_item: false,
        menu_title: "sidebar.cars",
      },
      {
        permissions: ["makes.delete", "makes.update", "makes.create"],

        path: "/cw/dashboard/makes",
        new_item: false,
        menu_title: "sidebar.makes",
      },
      {
        permissions: ["car_models.create", "car_models.update", "car_models.delete"],

        path: "/cw/dashboard/models",
        new_item: false,
        menu_title: "sidebar.models",
      },
      {
        permissions: ["car_versions.create", "car_versions.update", "car_versions.delete"],

        path: "/cw/dashboard/versions",
        new_item: false,
        menu_title: "sidebar.versions",
      },
      {
        permissions: [
          "users.list",
          "users.create",
          "users.show",
          "users.update",
          "users.activation",
          "users.delete",
        ],
        path: "/cw/dashboard/features",
        new_item: false,
        menu_title: "sidebar.features",
      },
    ],
  },
  {
    parent: true,
    menu_icon: "ti-id-badge",
    title: "Roles & Permissions",
    children: [
      {
        permissions: [
          "roles.list",
          "roles.create",
          "roles.show",
          "roles.update",
          "roles.activation",
          "roles.delete",
        ],
        path: "/cw/dashboard/roles",
        new_item: false,
        menu_title: "sidebar.roles",
      },
      {
        permissions: [
          "users.list",
          "users.create",
          "users.show",
          "users.update",
          "users.activation",
          "users.delete",
        ],
        path: "/cw/dashboard/users",
        new_item: false,
        menu_title: "sidebar.users",
      },
    ],
  },
  {
    parent: true,
    menu_icon: "zmdi zmdi-settings",
    title: "Settings",
    children: [
      {
        permissions: ["banners.delete", "banners.view", "banners.update", "banners.create"],
        path: "/cw/dashboard/banners",
        new_item: false,
        menu_title: "sidebar.banners",
      },

      {
        permissions: [
          "users.list",
          "users.create",
          "users.show",
          "users.update",
          "users.activation",
          "users.delete",
        ],
        path: "/cw/dashboard/extraservice",
        new_item: false,
        menu_title: "sidebar.service",
      },

      {
        permissions: ["coupons.list"],
        path: "/cw/dashboard/coupons",
        new_item: false,
        menu_title: "coupons",
      },
      {
        permissions: ["highly_requested_packages.list"],
        path: "/cw/dashboard/packages",
        new_item: false,
        menu_title: "sidebar.packages",
      },
      {
        permissions: ["loyalty_settings.list"],
        path: "/cw/dashboard/LoyaltyPoints",
        new_item: false,
        menu_title: "sidebar.LoyaltyPoints",
      },
      {
        path: "/cw/dashboard/Support",
        new_item: false,
        menu_title: "sidebar.support" ,
        permissions: [
          "feedbacks.list",
        ],
      },
     
    ],
  },
];
class SidebarContent extends Component {
  toggleMenu(menu, stateCategory) {
    const data = {
      menu,
      stateCategory,
    };
    this.props.onToggleMenu(data);
  }

  render() {
    return (
      <div className="rct-sidebar-nav my-2">
        <nav className="navigation">
          <List className="rct-mainMenu p-0 m-0 list-unstyled">
            {sideBarContent.map((menu, key) => (
              <NavMenuItem
                menu={menu}
                key={JSON.stringify(key)}
                onToggleMenu={() => this.toggleMenu(menu, "category1")}
                userCan={userCan}
              />
            ))}
          </List>
        </nav>
      </div>
    );
  }
}

SidebarContent.propTypes = {
  onToggleMenu: PropTypes.func,
};

// map state to props
const mapStateToProps = ({ sidebar }) => ({ sidebar });

export default withRouter(
  connect(mapStateToProps, {
    onToggleMenu,
  })(SidebarContent),
);
