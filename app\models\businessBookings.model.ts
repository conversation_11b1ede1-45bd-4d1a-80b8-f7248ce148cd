// Business Booking Accepted Offer Interface
export interface BusinessBookingAcceptedOffer {
  additionalKilometer: number;
  allyClass: string;
  allyCompanyId: string;
  allyCompanyName: string;
  allyRate: number;
  businessRentalId: string;
  carInsuranceFull: number;
  carInsuranceStandard: number;
  createdAt: string;
  id: string;
  kilometerPerMonth: number;
  offerPrice: number;
  policyAndConditions: string;
  status: string;
  statusLocalized: string;
}

// Main Business Booking Interface
export interface BusinessBooking {
  acceptedOffer?: BusinessBookingAcceptedOffer;
  assignedTo?: string;
  additionalNotes?: string;
  allyClass?: string;
  allyCompanyId?: string;
  allyRate?: number;
  arBusinessActivity?: string;
  arMakeName?: string;
  arModelName?: string;
  arPickUpCityName?: string;
  bookingNo: string;
  businessActivityId?: string;
  businessActivityName?: string;
  cancelledAt?: string;
  cancelledReason?: string;
  carImage?: string;
  carModelId?: string;
  carVersionId?: string;
  closedAt?: string;
  closedReason?: string;
  commercialRegistrationCertificate?: string;
  commercialRegistrationNo?: string;
  companyEmail?: string;
  companyName?: string;
  companyPhone?: string;
  createdAt: string;
  customerName: string;
  enBusinessActivity?: string;
  enMakeName?: string;
  enModelName?: string;
  enPickUpCityName?: string;
  id: string;
  insuranceId?: string;
  insuranceName?: string;
  makeId?: string;
  makeName?: string;
  modelName?: string;
  numberOfCars: number;
  numberOfMonths: number;
  otherCarName?: string;
  phone?: string;
  pickUpCityId?: string;
  pickUpCityName?: string;
  pickUpDatetime?: string;
  dropOffDatetime?: string;
  status: string;
  statusLocalized?: string;
  userId: string;
  pricePerMonth?: number;
  totalBookingPrice?: number;
  year?: number;
}

// Business Booking Request Interface (for business requests)
export interface BusinessBookingRequest {
  additionalNotes?: string;
  allyCompanyId?: string;
  bookingNo: string;
  businessActivityId?: string;
  businessActivityName?: string;
  carImage?: string;
  carModelId?: string;
  carVersionId?: string;
  commercialRegistrationCertificate?: string;
  commercialRegistrationNo?: string;
  companyEmail?: string;
  companyName?: string;
  companyPhone?: string;
  createdAt: string;
  id: string;
  insuranceId?: string;
  makeId?: string;
  makeName?: string;
  modelName?: string;
  numberOfCars: number;
  numberOfMonths: number;
  otherCarName?: string;
  phone?: string;
  pickUpCityId?: string;
  pickUpCityName?: string;
  pickUpDatetime?: string;
  userId: string;
  status: string;
  customerName: string;
  insuranceName?: string;
  year?: number;
}

// Metadata Interface
export interface BusinessBookingMetadata {
  currentPage: number;
  limitValue: number;
  totalCount: number;
  totalPages: number;
}

// Business Bookings Response Interface
export interface BusinessBookingsResponse {
  collection: BusinessBooking[];
  metadata: BusinessBookingMetadata;
}

// Business Requests Response Interface
export interface BusinessRequestsResponse {
  collection: BusinessBookingRequest[];
  metadata: BusinessBookingMetadata;
}

// Business Booking Filters Interface
export interface BusinessBookingFilters {
  bookingNo?: string;
  customerMobile?: string;
  customerName?: string;
  allyCompanyName?: string;
  customerNid?: string;
  dropOffDate?: string;
  dropOffDateTo?: string;
  pickUpDate?: string;
  pickUpDateFrom?: string;
  status?: string[];
  id?: string;
  makeId?: string[];
  pickUpCityId?: string[];
  allyCompanyId?: string;
}

// Business Booking Query Variables Interface
export interface BusinessBookingQueryVariables extends BusinessBookingFilters {
  page?: number;
  limit?: number;
  orderBy?: string;
  sortBy?: string;
}

// Business Booking Status Filter Options
export const BUSINESS_BOOKING_FILTER_STATUS = [
  'all',
  'confirmed',
  'car_received',
  'invoiced',
  'cancelled',
  'closed',
] as const;

export type BusinessBookingStatus = (typeof BUSINESS_BOOKING_FILTER_STATUS)[number];

// Business Booking Count Response Interface
export interface BusinessBookingCountResponse {
  all: Array<[string, number]>;
}

// Business Booking Assignment Variables
export interface AssignBusinessBookingVariables {
  businessRentalId: string;
  userId?: string;
}

// Business Booking Creation Variables
export interface CreateBusinessBookingVariables {
  additionalNotes?: string;
  carModelId?: string;
  carVersionId?: string;
  insuranceId: string;
  makeId?: string;
  numberOfCars: number;
  numberOfMonths: number;
  otherCarName?: string;
  pickUpCityId: string;
  pickUpDatetime: string;
  userId: string;
}

// Business Booking Update Variables
export interface UpdateBusinessBookingVariables extends CreateBusinessBookingVariables {
  businessRentalId: string;
}

// Business Booking Offer Creation Variables
export interface CreateBusinessBookingOfferVariables {
  businessRentalId?: string;
  additionalKilometer: number;
  allyCompanyId: string;
  carInsuranceFull?: number;
  carInsuranceStandard?: number;
  kilometerPerMonth: number;
  offerPrice: number;
}

// Table Column Configuration Type
export interface BusinessBookingTableColumn {
  id: string;
  label: string;
  minWidth?: number;
  align?: 'right' | 'left' | 'center';
  sortable?: boolean;
  format?: (value: any, row?: BusinessBooking) => string | JSX.Element;
}

// Sort Configuration
export interface BusinessBookingSortConfig {
  field: string;
  direction: 'asc' | 'desc';
}
