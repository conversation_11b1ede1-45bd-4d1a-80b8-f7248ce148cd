/* eslint-disable prettier/prettier */
/* eslint-disable eqeqeq */
/* eslint-disable react/prop-types */
/* eslint-disable no-nested-ternary */
import React, { useState, useEffect } from "react";
import { useIntl } from "react-intl";
import { useQuery } from "@apollo/client";
import Select from "react-select";
import { CircularProgress } from "@material-ui/core";
import { persist } from "constants/constants";

export default function GenericDropDown({
  loading,
  variables,
  setSelected,
  selected,
  error,
  valueAttribute,
  dataAttribute,
  onChange,
  required,
  multiple,
  setList,
  customCarVersionLabel,
  query,
  placeholder,
  ...props
}) {
  const { data, loading: loadingData } = useQuery(query, {
    variables: {
      limit: persist.higherUnlimited,
      ...variables,
    },
  });
  const { locale, formatMessage } = useIntl();
  const [options, setOptions] = useState([]);

  useEffect(() => {
    if (data) {
      setOptions([
        { value: "all", label: formatMessage({ id: "widgets.all" }) },
        ...data?.[dataAttribute]?.collection?.map(
          (x) =>
            ({
              value: x[valueAttribute],
              label: !customCarVersionLabel
                ? x[`${locale}Name`]
                : `${x.carModel.make[`${locale}Name`]} ${x.carModel[`${locale}Name`]} ${
                    x[`${locale}Name`]
                  } ${x.year}`,
            } || []),
        ),
      ]);
    }
  }, [data]);

  useEffect(() => {
    if (data && setList && dataAttribute) {
      setList(data?.[dataAttribute]?.collection?.length);
    }
  }, [data]);

  useEffect(() => {
    if (!selected) {
      onClear();
    }
  }, [selected]);

  const selectInputRef = React.useRef();

  const onClear = () => {
    selectInputRef.current.select.clearValue();
  };

  return (
    <Select
      key={options}
      className={`dropdown-select ${multiple ? "multiple" : ""}  ${required ? "required" : ""} ${
        error ? "selection-error" : ""
      }`}
      isDisabled={props?.isDisabled}
      isClearable
      options={options}
      ref={selectInputRef}
      isMulti={multiple}
      loadOptions={loadingData || loading}
      value={
        multiple
          ? options?.filter((optn) => selected?.includes(optn.value))
          : options.find((optn) => `${optn.value}` === `${selected}`)
      }
      placeholder={formatMessage({ id: placeholder })}
      onChange={(selection) => {
        if (!selection && multiple) {
          setSelected([]);
          return;
        }
        if (multiple) {
          if (
            (selection?.[0]?.value == "all" && selection?.length == 1) ||
            selection?.[selection?.length - 1]?.value == "all"
          ) {
            setSelected(["all"]);
            return;
          }
          const filtered = [...selection?.map((i) => i.value)]?.filter((i) => i != "all");
          setSelected(filtered);
          return;
        }
        setSelected(+selection?.value);
        onChange(+selection?.value);
      }}
      noOptionsMessage={() => {
        if (loadingData) {
          return <CircularProgress />;
        }
        if (!options?.length) return "no data found";
      }}
      {...props}
    />
  );
}
