'use client';
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import BusinessBookingsContent from './BusinessBookingsContent';
import { getAuthToken } from '../utils/cookies';
import PageLayout from '../PageLayout';

export default function BusinessBookingsPage() {
  const router = useRouter();

  useEffect(() => {
    // Check for token in cookies
    const token = getAuthToken();
    if (!token) {
      router.replace('/login');
    }
  }, [router]);

  return (
    <PageLayout>
      <BusinessBookingsContent />
    </PageLayout>
  );
}
