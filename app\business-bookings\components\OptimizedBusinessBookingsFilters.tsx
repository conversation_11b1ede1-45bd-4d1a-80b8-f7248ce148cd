'use client';
import React, { useState, useCallback, useMemo, memo, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Autocomplete,
  Typography,
  Divider,
  useTheme,
  InputAdornment,
  IconButton,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import SearchIcon from '@mui/icons-material/Search';
import ClearIcon from '@mui/icons-material/Clear';
import dayjs from 'dayjs';
import { BusinessBookingFilters } from '../../models/businessBookings.model';

interface OptimizedBusinessBookingsFiltersProps {
  onApply: (filters: BusinessBookingFilters) => void;
  onReset: () => void;
  initialFilters?: BusinessBookingFilters;
  loading?: boolean;
  makes: any[];
  areas: any[];
  companies: any[];
}

const OptimizedBusinessBookingsFilters = memo(
  ({
    onApply,
    onReset,
    initialFilters = {},
    loading = false,
    makes,
    areas,
    companies,
  }: OptimizedBusinessBookingsFiltersProps) => {
    const { t, i18n } = useTranslation();
    const isRTL = i18n.language === 'ar';
    const theme = useTheme();

    // Local filter state
    const [filters, setFilters] = useState<BusinessBookingFilters>({
      customerName: '',
      customerNid: '',
      bookingNo: '',
      customerMobile: '',
      allyCompanyName: '',
      status: [],
      makeId: [],
      pickUpCityId: [],
      allyCompanyId: '',
      pickUpDateFrom: '',
      pickUpDate: '',
      dropOffDate: '',
      dropOffDateTo: '',
      ...initialFilters,
    });

    // Update filters when initialFilters change
    useEffect(() => {
      setFilters((prev) => ({ ...prev, ...initialFilters }));
    }, [initialFilters]);

    // Handle input change
    const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
      const { name, value } = e.target;
      setFilters((prev) => ({ ...prev, [name]: value }));
    }, []);

    // Handle autocomplete change
    const handleAutocompleteChange = useCallback((name: string, value: any) => {
      if (['status', 'makeId', 'pickUpCityId'].includes(name)) {
        const arrayValue = Array.isArray(value) ? value : value ? [value] : [];
        setFilters((prev) => ({ ...prev, [name]: arrayValue }));
      } else {
        setFilters((prev) => ({ ...prev, [name]: value || '' }));
      }
    }, []);

    // Handle date change
    const handleDateChange = useCallback((name: string, value: any) => {
      const dateValue = value ? dayjs(value).format('YYYY-MM-DD') : '';
      setFilters((prev) => ({ ...prev, [name]: dateValue }));
    }, []);

    // Handle form submit
    const handleSubmit = useCallback(
      (e: React.FormEvent) => {
        e.preventDefault();

        // Remove empty values
        const cleanedFilters: Partial<BusinessBookingFilters> = {};
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== '' && value !== null && value !== undefined) {
            if (Array.isArray(value) && value.length > 0) {
              cleanedFilters[key as keyof BusinessBookingFilters] = value as any;
            } else if (!Array.isArray(value)) {
              cleanedFilters[key as keyof BusinessBookingFilters] = value as any;
            }
          }
        });

        onApply(cleanedFilters as BusinessBookingFilters);
      },
      [filters, onApply]
    );

    // Handle form reset
    const handleReset = useCallback(() => {
      setFilters({
        customerName: '',
        customerNid: '',
        bookingNo: '',
        customerMobile: '',
        allyCompanyName: '',
        status: [],
        makeId: [],
        pickUpCityId: [],
        allyCompanyId: '',
        pickUpDateFrom: '',
        pickUpDate: '',
        dropOffDate: '',
        dropOffDateTo: '',
      });
      onReset();
    }, [onReset]);

    // Clear a single field
    const clearField = useCallback((name: string) => {
      if (['pickUpDateFrom', 'pickUpDate', 'dropOffDate', 'dropOffDateTo'].includes(name)) {
        setFilters((prev) => ({ ...prev, [name]: '' }));
      } else if (['status', 'makeId', 'pickUpCityId'].includes(name)) {
        setFilters((prev) => ({ ...prev, [name]: [] }));
      } else {
        setFilters((prev) => ({ ...prev, [name]: '' }));
      }
    }, []);

    // Memoized options
    const makeOptions = useMemo(
      () =>
        makes.map((make: any) => ({
          id: make.id,
          label: isRTL ? make.arName || make.name : make.enName || make.name,
          value: make.id,
          arLabel: make.arName,
        })),
      [makes, isRTL]
    );

    const areaOptions = useMemo(
      () =>
        areas.map((area: any) => ({
          id: area.id,
          label: isRTL ? area.arName || area.name : area.enName || area.name,
          value: area.id,
          arLabel: area.arName,
        })),
      [areas, isRTL]
    );

    const companyOptions = useMemo(
      () =>
        companies.map((company: any) => ({
          id: company.id,
          label: isRTL ? company.arName || company.name : company.enName || company.name,
          value: company.id,
          arLabel: company.arName,
        })),
      [companies, isRTL]
    );

    const statusOptions = useMemo(
      () => [
        { id: 'confirmed', label: t('CONFIRMED'), value: 'confirmed' },
        { id: 'car_received', label: t('CAR_RECEIVED'), value: 'car_received' },
        { id: 'invoiced', label: t('INVOICED'), value: 'invoiced' },
        { id: 'cancelled', label: t('CANCELLED'), value: 'cancelled' },
        { id: 'closed', label: t('CLOSED'), value: 'closed' },
        { id: 'pending', label: t('PENDING'), value: 'pending' },
      ],
      [t]
    );

    return (
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <Box
          component="form"
          onSubmit={handleSubmit}
          noValidate
          sx={{ p: 3, backgroundColor: theme.palette.background.paper }}
        >
          <Typography variant="h6" gutterBottom sx={{ mb: 3, fontWeight: 600 }}>
            {t('Filters')}
          </Typography>

          {/* Search Fields */}
          <Box sx={{ mb: 3 }}>
            <Typography
              variant="subtitle2"
              gutterBottom
              sx={{ fontWeight: 600, color: 'primary.main' }}
            >
              {t('Search Fields')}
            </Typography>
            <Divider sx={{ mb: 2 }} />

            <Grid container spacing={3}>
              <Grid item xs={12} sm={6} md={4}>
                <TextField
                  fullWidth
                  id="customerName"
                  name="customerName"
                  label={t('Customer Name')}
                  placeholder={t('customerName.placeholder')}
                  value={filters.customerName || ''}
                  onChange={handleInputChange}
                  variant="outlined"
                  size="small"
                  InputProps={{
                    endAdornment: filters.customerName ? (
                      <InputAdornment position="end">
                        <IconButton
                          aria-label="clear customer name"
                          onClick={() => clearField('customerName')}
                          edge="end"
                          size="small"
                        >
                          <ClearIcon fontSize="small" />
                        </IconButton>
                      </InputAdornment>
                    ) : null,
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4}>
                <TextField
                  fullWidth
                  id="customerNid"
                  name="customerNid"
                  label={t('Customer NID')}
                  placeholder={t('nid.placeholder')}
                  value={filters.customerNid || ''}
                  onChange={handleInputChange}
                  variant="outlined"
                  size="small"
                  InputProps={{
                    endAdornment: filters.customerNid ? (
                      <InputAdornment position="end">
                        <IconButton
                          aria-label="clear customer nid"
                          onClick={() => clearField('customerNid')}
                          edge="end"
                          size="small"
                        >
                          <ClearIcon fontSize="small" />
                        </IconButton>
                      </InputAdornment>
                    ) : null,
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4}>
                <TextField
                  fullWidth
                  id="bookingNo"
                  name="bookingNo"
                  label={t('Booking Number')}
                  placeholder={t('bookingNo.placeholder')}
                  value={filters.bookingNo || ''}
                  onChange={handleInputChange}
                  variant="outlined"
                  size="small"
                  InputProps={{
                    endAdornment: filters.bookingNo ? (
                      <InputAdornment position="end">
                        <IconButton
                          aria-label="clear booking number"
                          onClick={() => clearField('bookingNo')}
                          edge="end"
                          size="small"
                        >
                          <ClearIcon fontSize="small" />
                        </IconButton>
                      </InputAdornment>
                    ) : null,
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4}>
                <TextField
                  fullWidth
                  id="customerMobile"
                  name="customerMobile"
                  label={t('Customer Mobile')}
                  placeholder={t('customerMobile.placeholder')}
                  value={filters.customerMobile || ''}
                  onChange={handleInputChange}
                  variant="outlined"
                  size="small"
                  InputProps={{
                    endAdornment: filters.customerMobile ? (
                      <InputAdornment position="end">
                        <IconButton
                          aria-label="clear customer mobile"
                          onClick={() => clearField('customerMobile')}
                          edge="end"
                          size="small"
                        >
                          <ClearIcon fontSize="small" />
                        </IconButton>
                      </InputAdornment>
                    ) : null,
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4}>
                <TextField
                  fullWidth
                  id="allyCompanyName"
                  name="allyCompanyName"
                  label={t('Ally Company Name')}
                  placeholder={t('allyCompanyName.placeholder')}
                  value={filters.allyCompanyName || ''}
                  onChange={handleInputChange}
                  variant="outlined"
                  size="small"
                  InputProps={{
                    endAdornment: filters.allyCompanyName ? (
                      <InputAdornment position="end">
                        <IconButton
                          aria-label="clear ally company name"
                          onClick={() => clearField('allyCompanyName')}
                          edge="end"
                          size="small"
                        >
                          <ClearIcon fontSize="small" />
                        </IconButton>
                      </InputAdornment>
                    ) : null,
                  }}
                />
              </Grid>
            </Grid>
          </Box>

          {/* Filter Options */}
          <Box sx={{ mb: 3 }}>
            <Typography
              variant="subtitle2"
              gutterBottom
              sx={{ fontWeight: 600, color: 'primary.main' }}
            >
              {t('Filter Options')}
            </Typography>
            <Divider sx={{ mb: 2 }} />

            <Grid container spacing={3}>
              <Grid item xs={12} sm={6} md={4}>
                <Autocomplete
                  multiple
                  options={statusOptions}
                  getOptionLabel={(option) => option.label}
                  value={statusOptions.filter((opt) => filters.status?.includes(opt.value))}
                  onChange={(_, newValue) =>
                    handleAutocompleteChange(
                      'status',
                      newValue.map((item) => item.value)
                    )
                  }
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label={t('Booking Status')}
                      variant="outlined"
                      size="small"
                    />
                  )}
                  size="small"
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4}>
                <Autocomplete
                  multiple
                  options={makeOptions}
                  getOptionLabel={(option) =>
                    isRTL && option.arLabel ? option.arLabel : option.label
                  }
                  value={makeOptions.filter((opt) => filters.makeId?.includes(opt.value))}
                  onChange={(_, newValue) =>
                    handleAutocompleteChange(
                      'makeId',
                      newValue.map((item) => item.value)
                    )
                  }
                  renderInput={(params) => (
                    <TextField {...params} label={t('Car Make')} variant="outlined" size="small" />
                  )}
                  size="small"
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4}>
                <Autocomplete
                  multiple
                  options={areaOptions}
                  getOptionLabel={(option) =>
                    isRTL && option.arLabel ? option.arLabel : option.label
                  }
                  value={areaOptions.filter((opt) => filters.pickUpCityId?.includes(opt.value))}
                  onChange={(_, newValue) =>
                    handleAutocompleteChange(
                      'pickUpCityId',
                      newValue.map((item) => item.value)
                    )
                  }
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label={t('Pickup City')}
                      variant="outlined"
                      size="small"
                    />
                  )}
                  size="small"
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4}>
                <Autocomplete
                  options={companyOptions}
                  getOptionLabel={(option) =>
                    isRTL && option.arLabel ? option.arLabel : option.label
                  }
                  value={companyOptions.find((opt) => opt.value === filters.allyCompanyId) || null}
                  onChange={(_, newValue) =>
                    handleAutocompleteChange('allyCompanyId', newValue?.value || '')
                  }
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label={t('Ally Company')}
                      variant="outlined"
                      size="small"
                    />
                  )}
                  size="small"
                />
              </Grid>
            </Grid>
          </Box>

          {/* Date Filters */}
          <Box sx={{ mb: 3 }}>
            <Typography
              variant="subtitle2"
              gutterBottom
              sx={{ fontWeight: 600, color: 'primary.main' }}
            >
              {t('Date Filters')}
            </Typography>
            <Divider sx={{ mb: 2 }} />

            <Grid container spacing={3}>
              <Grid item xs={12} sm={6} md={3}>
                <DatePicker
                  label={t('Pickup Date From')}
                  value={filters.pickUpDateFrom ? dayjs(filters.pickUpDateFrom) : null}
                  onChange={(newValue) => handleDateChange('pickUpDateFrom', newValue)}
                  slotProps={{
                    textField: {
                      size: 'small',
                      fullWidth: true,
                      variant: 'outlined',
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <DatePicker
                  label={t('Pickup Date To')}
                  value={filters.pickUpDate ? dayjs(filters.pickUpDate) : null}
                  onChange={(newValue) => handleDateChange('pickUpDate', newValue)}
                  slotProps={{
                    textField: {
                      size: 'small',
                      fullWidth: true,
                      variant: 'outlined',
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <DatePicker
                  label={t('Drop-off Date From')}
                  value={filters.dropOffDate ? dayjs(filters.dropOffDate) : null}
                  onChange={(newValue) => handleDateChange('dropOffDate', newValue)}
                  slotProps={{
                    textField: {
                      size: 'small',
                      fullWidth: true,
                      variant: 'outlined',
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <DatePicker
                  label={t('Drop-off Date To')}
                  value={filters.dropOffDateTo ? dayjs(filters.dropOffDateTo) : null}
                  onChange={(newValue) => handleDateChange('dropOffDateTo', newValue)}
                  slotProps={{
                    textField: {
                      size: 'small',
                      fullWidth: true,
                      variant: 'outlined',
                    },
                  }}
                />
              </Grid>
            </Grid>
          </Box>

          {/* Action Buttons */}
          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end', mt: 3 }}>
            <Button
              variant="outlined"
              onClick={handleReset}
              startIcon={<ClearIcon />}
              disabled={loading}
            >
              {t('Reset')}
            </Button>
            <Button type="submit" variant="contained" startIcon={<SearchIcon />} disabled={loading}>
              {t('Apply Filters')}
            </Button>
          </Box>
        </Box>
      </LocalizationProvider>
    );
  }
);

OptimizedBusinessBookingsFilters.displayName = 'OptimizedBusinessBookingsFilters';

export default OptimizedBusinessBookingsFilters;
