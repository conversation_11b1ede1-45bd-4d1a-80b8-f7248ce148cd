'use client';
import React, { useState, useCallback, useMemo, memo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Autocomplete,
  Typography,
  Divider,
  useTheme,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import SearchIcon from '@mui/icons-material/Search';
import ClearIcon from '@mui/icons-material/Clear';
import dayjs, { Dayjs } from 'dayjs';
import { BusinessBookingFilters } from '../../models/businessBookings.model';

interface FilterOption {
  id: string | number;
  label: string;
  value: string | number;
  arLabel?: string;
}

interface OptimizedBusinessBookingsFiltersProps {
  filters: BusinessBookingFilters;
  onFiltersChange: (filters: BusinessBookingFilters) => void;
  onApply: () => void;
  onReset: () => void;
  loading?: boolean;
  makes: any[];
  areas: any[];
  companies: any[];
}

// Memoized text field component
const MemoizedTextField = memo(
  ({
    id,
    label,
    placeholder,
    value,
    onChange,
  }: {
    id: string;
    label: string;
    placeholder: string;
    value: string;
    onChange: (value: string) => void;
  }) => {
    const handleChange = useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
        onChange(e.target.value);
      },
      [onChange]
    );

    return (
      <TextField
        fullWidth
        label={label}
        placeholder={placeholder}
        value={value || ''}
        onChange={handleChange}
        variant="outlined"
        size="small"
      />
    );
  }
);

MemoizedTextField.displayName = 'MemoizedTextField';

// Memoized autocomplete component
const MemoizedAutocomplete = memo(
  ({
    id,
    label,
    options,
    value,
    onChange,
    multiple = false,
    isRTL = false,
  }: {
    id: string;
    label: string;
    options: FilterOption[];
    value: any;
    onChange: (value: any) => void;
    multiple?: boolean;
    isRTL?: boolean;
  }) => {
    const handleChange = useCallback(
      (_, newValue: any) => {
        if (multiple) {
          const values = Array.isArray(newValue)
            ? newValue.map((item: FilterOption) => item.value)
            : [];
          onChange(values);
        } else {
          const singleValue = newValue as FilterOption | null;
          onChange(singleValue?.value || '');
        }
      },
      [onChange, multiple]
    );

    const currentValue = useMemo(() => {
      if (multiple) {
        return options.filter((opt) => Array.isArray(value) && value.includes(opt.value));
      } else {
        return options.find((opt) => opt.value === value) || null;
      }
    }, [options, value, multiple]);

    return (
      <Autocomplete
        multiple={multiple}
        options={options}
        getOptionLabel={(option) => (isRTL && option.arLabel ? option.arLabel : option.label)}
        value={currentValue}
        onChange={handleChange}
        renderInput={(params) => (
          <TextField {...params} label={label} variant="outlined" size="small" />
        )}
        size="small"
      />
    );
  }
);

MemoizedAutocomplete.displayName = 'MemoizedAutocomplete';

// Memoized date picker component
const MemoizedDatePicker = memo(
  ({
    label,
    value,
    onChange,
  }: {
    label: string;
    value: string;
    onChange: (value: string) => void;
  }) => {
    const [dateValue, setDateValue] = useState<Dayjs | null>(value ? dayjs(value) : null);

    const handleChange = useCallback(
      (newValue: Dayjs | null) => {
        setDateValue(newValue);
        const dateString = newValue ? newValue.format('YYYY-MM-DD') : '';
        onChange(dateString);
      },
      [onChange]
    );

    return (
      <DatePicker
        label={label}
        value={dateValue}
        onChange={handleChange}
        slotProps={{
          textField: {
            size: 'small',
            fullWidth: true,
            variant: 'outlined',
          },
        }}
      />
    );
  }
);

MemoizedDatePicker.displayName = 'MemoizedDatePicker';

const OptimizedBusinessBookingsFilters = memo(
  ({
    filters,
    onFiltersChange,
    onApply,
    onReset,
    loading = false,
    makes,
    areas,
    companies,
  }: OptimizedBusinessBookingsFiltersProps) => {
    const { t, i18n } = useTranslation();
    const isRTL = i18n.language === 'ar';
    const theme = useTheme();

    // Memoized filter change handlers
    const handleFieldChange = useCallback(
      (fieldId: string) => (value: any) => {
        onFiltersChange({
          ...filters,
          [fieldId]: value,
        });
      },
      []
    );

    // Memoized options
    const makeOptions = useMemo(
      () =>
        makes.map((make: any) => ({
          id: make.id,
          label: isRTL ? make.arName || make.name : make.enName || make.name,
          value: make.id,
          arLabel: make.arName,
        })),
      [makes, isRTL]
    );

    const areaOptions = useMemo(
      () =>
        areas.map((area: any) => ({
          id: area.id,
          label: isRTL ? area.arName || area.name : area.enName || area.name,
          value: area.id,
          arLabel: area.arName,
        })),
      [areas, isRTL]
    );

    const companyOptions = useMemo(
      () =>
        companies.map((company: any) => ({
          id: company.id,
          label: isRTL ? company.arName || company.name : company.enName || company.name,
          value: company.id,
          arLabel: company.arName,
        })),
      [companies, isRTL]
    );

    const statusOptions = useMemo(
      () => [
        { id: 'confirmed', label: t('CONFIRMED'), value: 'confirmed' },
        { id: 'car_received', label: t('CAR_RECEIVED'), value: 'car_received' },
        { id: 'invoiced', label: t('INVOICED'), value: 'invoiced' },
        { id: 'cancelled', label: t('CANCELLED'), value: 'cancelled' },
        { id: 'closed', label: t('CLOSED'), value: 'closed' },
        { id: 'pending', label: t('PENDING'), value: 'pending' },
      ],
      [t]
    );

    return (
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <Box sx={{ p: 3, backgroundColor: theme.palette.background.paper }}>
          <Typography variant="h6" gutterBottom sx={{ mb: 3, fontWeight: 600 }}>
            {t('Filters')}
          </Typography>

          {/* Search Fields */}
          <Box sx={{ mb: 3 }}>
            <Typography
              variant="subtitle2"
              gutterBottom
              sx={{ fontWeight: 600, color: 'primary.main' }}
            >
              {t('Search Fields')}
            </Typography>
            <Divider sx={{ mb: 2 }} />

            <Grid container spacing={3}>
              <Grid item xs={12} sm={6} md={4}>
                <MemoizedTextField
                  id="customerName"
                  label={t('Customer Name')}
                  placeholder={t('customerName.placeholder')}
                  value={filters.customerName || ''}
                  onChange={handleFieldChange('customerName')}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4}>
                <MemoizedTextField
                  id="customerNid"
                  label={t('Customer NID')}
                  placeholder={t('nid.placeholder')}
                  value={filters.customerNid || ''}
                  onChange={handleFieldChange('customerNid')}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4}>
                <MemoizedTextField
                  id="bookingNo"
                  label={t('Booking Number')}
                  placeholder={t('bookingNo.placeholder')}
                  value={filters.bookingNo || ''}
                  onChange={handleFieldChange('bookingNo')}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4}>
                <MemoizedTextField
                  id="customerMobile"
                  label={t('Customer Mobile')}
                  placeholder={t('customerMobile.placeholder')}
                  value={filters.customerMobile || ''}
                  onChange={handleFieldChange('customerMobile')}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4}>
                <MemoizedTextField
                  id="allyCompanyName"
                  label={t('Ally Company Name')}
                  placeholder={t('allyCompanyName.placeholder')}
                  value={filters.allyCompanyName || ''}
                  onChange={handleFieldChange('allyCompanyName')}
                />
              </Grid>
            </Grid>
          </Box>

          {/* Filter Options */}
          <Box sx={{ mb: 3 }}>
            <Typography
              variant="subtitle2"
              gutterBottom
              sx={{ fontWeight: 600, color: 'primary.main' }}
            >
              {t('Filter Options')}
            </Typography>
            <Divider sx={{ mb: 2 }} />

            <Grid container spacing={3}>
              <Grid item xs={12} sm={6} md={4}>
                <MemoizedAutocomplete
                  id="status"
                  label={t('Booking Status')}
                  options={statusOptions}
                  value={filters.status}
                  onChange={handleFieldChange('status')}
                  multiple={true}
                  isRTL={isRTL}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4}>
                <MemoizedAutocomplete
                  id="makeId"
                  label={t('Car Make')}
                  options={makeOptions}
                  value={filters.makeId}
                  onChange={handleFieldChange('makeId')}
                  multiple={true}
                  isRTL={isRTL}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4}>
                <MemoizedAutocomplete
                  id="pickUpCityId"
                  label={t('Pickup City')}
                  options={areaOptions}
                  value={filters.pickUpCityId}
                  onChange={handleFieldChange('pickUpCityId')}
                  multiple={true}
                  isRTL={isRTL}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4}>
                <MemoizedAutocomplete
                  id="allyCompanyId"
                  label={t('Ally Company')}
                  options={companyOptions}
                  value={filters.allyCompanyId}
                  onChange={handleFieldChange('allyCompanyId')}
                  multiple={false}
                  isRTL={isRTL}
                />
              </Grid>
            </Grid>
          </Box>

          {/* Date Filters */}
          <Box sx={{ mb: 3 }}>
            <Typography
              variant="subtitle2"
              gutterBottom
              sx={{ fontWeight: 600, color: 'primary.main' }}
            >
              {t('Date Filters')}
            </Typography>
            <Divider sx={{ mb: 2 }} />

            <Grid container spacing={3}>
              <Grid item xs={12} sm={6} md={3}>
                <MemoizedDatePicker
                  label={t('Pickup Date From')}
                  value={filters.pickUpDateFrom || ''}
                  onChange={handleFieldChange('pickUpDateFrom')}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <MemoizedDatePicker
                  label={t('Pickup Date To')}
                  value={filters.pickUpDate || ''}
                  onChange={handleFieldChange('pickUpDate')}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <MemoizedDatePicker
                  label={t('Drop-off Date From')}
                  value={filters.dropOffDate || ''}
                  onChange={handleFieldChange('dropOffDate')}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <MemoizedDatePicker
                  label={t('Drop-off Date To')}
                  value={filters.dropOffDateTo || ''}
                  onChange={handleFieldChange('dropOffDateTo')}
                />
              </Grid>
            </Grid>
          </Box>

          {/* Action Buttons */}
          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end', mt: 3 }}>
            <Button
              variant="outlined"
              onClick={onReset}
              startIcon={<ClearIcon />}
              disabled={loading}
            >
              {t('Reset')}
            </Button>
            <Button
              variant="contained"
              onClick={onApply}
              startIcon={<SearchIcon />}
              disabled={loading}
            >
              {t('Apply Filters')}
            </Button>
          </Box>
        </Box>
      </LocalizationProvider>
    );
  }
);

OptimizedBusinessBookingsFilters.displayName = 'OptimizedBusinessBookingsFilters';

export default OptimizedBusinessBookingsFilters;
