import { gql } from '@apollo/client';

export const GET_ALL_COMPANIES = gql`
  query GetAllCompanies(
    $isActive: Boolean
    $limit: Int
    $page: Int
    $pickupCityId: Int
    $dropoffCityId: Int
    $cityId: [Int!]
    $email: String
    $managerName: String
    $phoneNumber: String
    $allyClass: String
    $allyCompanyId: ID
    $isRentToOwn: Boolean
    $pickStartDate: String
  ) {
    allyCompanies(
      isActive: $isActive
      limit: $limit
      page: $page
      pickupCityId: $pickupCityId
      dropoffCityId: $dropoffCityId
      cityId: $cityId
      email: $email
      managerName: $managerName
      phoneNumber: $phoneNumber
      allyClass: $allyClass
      allyCompanyId: $allyCompanyId
      isRentToOwn: $isRentToOwn
      pickStartDate: $pickStartDate
    ) {
      collection {
        id
        arName
        enName
        email
        phoneNumber
        managerName
        logo
        isActive
        allyClass
        isB2c
        addedBy
      }
      metadata {
        currentPage
        limitValue
        totalCount
        totalPages
      }
    }
  }
`;

export const GET_AVAILABLE_COMPANIES = gql`
  query GetAvailableCompanies(
    $isActive: Boolean
    $limit: Int
    $page: Int
    $pickupCityId: Int
    $dropoffCityId: Int
    $cityId: Int
    $email: String
    $managerName: String
    $phoneNumber: String
    $allyClass: String
    $allyCompanyId: ID
    $isRentToOwn: Boolean
    $pickStartDate: String
  ) {
    availableAllyCompanies(
      isActive: $isActive
      limit: $limit
      page: $page
      pickupCityId: $pickupCityId
      dropoffCityId: $dropoffCityId
      cityId: $cityId
      email: $email
      managerName: $managerName
      phoneNumber: $phoneNumber
      allyClass: $allyClass
      allyCompanyId: $allyCompanyId
      isRentToOwn: $isRentToOwn
      pickStartDate: $pickStartDate
    ) {
      collection {
        id
        arName
        enName
        email
        phoneNumber
        managerName
        logo
        isActive
        allyClass
        isB2c
        addedBy
      }
      metadata {
        currentPage
        limitValue
        totalCount
        totalPages
      }
    }
  }
`;

export const GET_COMPANY_BY_ID = gql`
  query GetCompanyById($id: ID!) {
    allyCompany(id: $id) {
      id
      arName
      enName
      email
      phoneNumber
      managerName
      logo
      isActive
      allyClass
      isB2c
      addedBy
      createdAt
      updatedAt
    }
  }
`;

export const GET_COMPANIES_NAME = gql`
  query GetCompaniesName {
    allyCompanies(limit: 1000) {
      collection {
        id
        arName
        enName
        name
      }
    }
  }
`;

export const GET_AVAILABLE_BRANCHES = gql`
  query GetAvailableBranches(
    $limit: Int
    $page: Int
    $isActive: Boolean
    $branchId: ID
    $allyCompanyIds: [ID!]
    $areaIds: [ID!]
    $isDeleted: Boolean
    $isRentToOwn: Boolean
    $pickStartDate: String
  ) {
    availableBranches(
      limit: $limit
      page: $page
      isActive: $isActive
      branchId: $branchId
      areaIds: $areaIds
      allyCompanyIds: $allyCompanyIds
      isDeleted: $isDeleted
      isRentToOwn: $isRentToOwn
      pickStartDate: $pickStartDate
    ) {
      collection {
        id
        name
        arName
        enName
        isActive
        canHandover
        canDelivery
        officeNumber
        area {
          id
          arName
          enName
        }
        branchExtraServices {
          id
          isActive
          isRequired
          payType
          serviceValue
          totalServiceValue
          arSubtitle
          enSubtitle
          subtitle
          branchId
          allyExtraService {
            extraService {
              arTitle
              enTitle
            }
          }
        }
        branchDeliveryPrices {
          deliveryPrice
          handoverPrice
          from
          to
        }
        allyCompany {
          id
          arName
          enName
          email
          allyExtraServicesForAlly {
            id
            isActive
            isRequired
            payType
            serviceValue
            showFor
            subtitle
            totalServiceValue
            allyCompanyId
            arSubtitle
            enSubtitle
            extraServiceId
            extraService {
              arTitle
              enTitle
            }
          }
          allyHandoverCities {
            pickUpCityId
            dropOffCityId
            price
          }
        }
      }
      metadata {
        currentPage
        limitValue
        totalCount
        totalPages
      }
    }
  }
`;
